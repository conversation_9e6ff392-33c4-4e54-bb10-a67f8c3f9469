namespace CPS.Supplier.Application.Common.Configuration;

public class AzureStorageSettings
{
    public const string SectionName = "AzureStorage";

    public string ConnectionString { get; set; } = string.Empty;
    public string BlobContainerName { get; set; } = "supplier-incoming-files";
    public string QueueName { get; set; } = "supplier-incoming-messages";
    public string ArchivedFilesContainer { get; set; } = "supplier-archived-files";
}