﻿using CPS.Supplier.Application.Common.Configuration;
using CPS.Supplier.Application.Models.AdministrationApi;
using CPS.Supplier.Application.Services.Auth0;
using CPS.Supplier.Application.Services.Auth0.Contracts;
using CPS.Supplier.Application.Services.Common;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Application.Services.DataFile;
using CPS.Supplier.Application.Services.DataFile.Contracts;
using CPS.Supplier.Application.Services.Localization;
using CPS.Supplier.Application.Services.MasterData;
using CPS.Supplier.Application.Services.MasterData.Contracts;
using CPS.Supplier.Application.Services.PasswordReset;
using CPS.Supplier.Application.Services.PasswordReset.Contracts;
using CPS.Supplier.Application.Services.Supplier;
using CPS.Supplier.Application.Services.Supplier.Contracts;
using CPS.Supplier.Application.Services.User;
using CPS.Supplier.Application.Services.User.Contracts;
using CPS.Supplier.Application.Services.UserContext;
using CPS.Supplier.Application.Services.UserContext.Contracts;
using CPS.Supplier.Application.Services.VolumeFileProcess;
using CPS.Supplier.Application.Services.VolumeFileProcess.Contracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Options;
using ConfigAuth0Settings = CPS.Supplier.Application.Common.Configuration.Auth0Settings;

namespace CPS.Supplier.Application
{
    public static class ApplicationServiceRegistration
    {
        public static IServiceCollection AddApplicationServices(this IServiceCollection services, IConfiguration configuration)
        {
            AddConfigurationServices(services, configuration);
            AddApplicationBusinessServices(services);

            return services;
        }

        private static void AddConfigurationServices(IServiceCollection services, IConfiguration configuration)
        {
            // Configuration with validation - Administration API
            services.Configure<AdministrationApiSettings>(
                configuration.GetSection(AdministrationApiSettings.SectionName));
            services.AddSingleton<IValidateOptions<AdministrationApiSettings>, AdministrationApiSettingsValidator>();

            // Configuration with validation - Core Settings
            services.AddOptions<ConnectionStringsSettings>()
                .Bind(configuration.GetSection(ConnectionStringsSettings.SectionName))
                .ValidateOnStart();

            services.AddOptions<ConfigAuth0Settings>()
                .Bind(configuration.GetSection(ConfigAuth0Settings.SectionName))
                .ValidateOnStart();

            services.AddOptions<AzureStorageSettings>()
                .Bind(configuration.GetSection(AzureStorageSettings.SectionName))
                .ValidateOnStart();

            services.AddOptions<SftpSettings>()
                .Bind(configuration.GetSection(SftpSettings.SectionName))
                .ValidateOnStart();

            services.AddOptions<KeyVaultSettings>()
                .Bind(configuration.GetSection(KeyVaultSettings.SectionName))
                .ValidateOnStart();

            // Optional configuration (no validation required)
            services.Configure<FileStorageOptions>(
                configuration.GetSection(FileStorageOptions.SectionName));

            services.Configure<CorsSettings>(
                configuration.GetSection(CorsSettings.SectionName));

            services.Configure<ApplicationInsightsSettings>(
                configuration.GetSection(ApplicationInsightsSettings.SectionName));

            services.Configure<SupplierApiSettings>(
                configuration.GetSection(SupplierApiSettings.SectionName));
        }

        private static void AddApplicationBusinessServices(IServiceCollection services)
        {
            services.TryAddTransient<ISupplierService, SupplierService>();
            services.TryAddTransient<IDataFileService, DataFileService>();
            services.TryAddTransient<IModelValidationService, ModelValidationService>();
            services.AddValidatorsFromAssembly(typeof(ApplicationServiceRegistration).Assembly);
            services.TryAddTransient<IUserContextService, UserContextService>();
            services.TryAddTransient<IAuth0TokenService, Auth0TokenService>();
            services.TryAddTransient<IHttpClientService, HttpClientService>();
            services.TryAddTransient<IMasterDataService, MasterDataService>();
            services.TryAddTransient<IPasswordResetService, PasswordResetService>();
            services.TryAddTransient<IUserService, UserService>();
            services.TryAddTransient<ICurrentUserService, CurrentUserService>();

            // Supplier file processing services
            services.TryAddTransient<IVolumeFileProcessService, VolumeFileProcessService>();

            services.TryAddTransient<ILocalizationService, LocalizationService>();
        }
    }
}