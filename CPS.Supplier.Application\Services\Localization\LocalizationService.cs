using CPS.Supplier.Application.Resources;
using Microsoft.Extensions.Localization;

namespace CPS.Supplier.Application.Services.Localization;

public class LocalizationService(IStringLocalizer<Messages> localizer) : ILocalizationService
{
    private readonly IStringLocalizer _localizer = localizer;

    public string GetString(string key)
    {
        var localizedString = _localizer[key];
        return localizedString.ResourceNotFound ? key : localizedString.Value;
    }

    public string GetString(string key, params object[] args)
    {
        var localizedString = _localizer[key, args];
        return localizedString.ResourceNotFound ? key : localizedString.Value;
    }
}