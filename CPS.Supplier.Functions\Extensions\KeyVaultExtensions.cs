using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using CPS.Supplier.Application.Common.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace CPS.Supplier.Functions.Extensions;

public static class KeyVaultExtensions
{
    public static IServiceCollection AddKeyVaultServices(this IServiceCollection services, IConfiguration configuration, IHostEnvironment environment)
    {
        var keyVaultSettings = configuration.GetSection(KeyVaultSettings.SectionName).Get<KeyVaultSettings>() ?? new KeyVaultSettings();

        if (!environment.IsDevelopment() && keyVaultSettings.Enabled && !string.IsNullOrEmpty(keyVaultSettings.Endpoint))
        {
            services.AddSingleton(_ => new SecretClient(
                new Uri(keyVaultSettings.Endpoint),
                new DefaultAzureCredential()));
        }

        return services;
    }

    public static void AddKeyVaultConfiguration(this IConfigurationBuilder configurationBuilder, IHostEnvironment environment)
    {
        var tempConfig = configurationBuilder.Build();
        var keyVaultSettings = tempConfig.GetSection(KeyVaultSettings.SectionName).Get<KeyVaultSettings>() ?? new KeyVaultSettings();

        if (!environment.IsDevelopment() && keyVaultSettings.Enabled && !string.IsNullOrEmpty(keyVaultSettings.Endpoint))
        {
            configurationBuilder.AddAzureKeyVault(keyVaultSettings.Endpoint);
        }
    }
}