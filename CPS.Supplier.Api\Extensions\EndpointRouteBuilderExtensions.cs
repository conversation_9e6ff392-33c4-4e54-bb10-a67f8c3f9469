﻿using System.Diagnostics.CodeAnalysis;

namespace CPS.Supplier.Api.Extensions
{
    internal static class EndpointRouteBuilderExtensions
    {
        public static IEndpointRouteBuilder MapGet(this IEndpointRouteBuilder builder, Delegate handler, [StringSyntax("Route")] string pattern = "")
        {

            builder.MapGet(pattern, handler)
                .WithName(handler.Method.Name);

            return builder;
        }

        public static IEndpointRouteBuilder MapPost(this IEndpointRouteBuilder builder, Delegate handler, [StringSyntax("Route")] string pattern = "")
        {
            builder.MapPost(pattern, handler)
                .WithName(handler.Method.Name);

            return builder;
        }

        public static IEndpointRouteBuilder MapPut(this IEndpointRouteBuilder builder, Delegate handler, [StringSyntax("Route")] string pattern)
        {
            builder.MapPut(pattern, handler)
                .WithName(handler.Method.Name);

            return builder;
        }

        public static IEndpointRouteBuilder MapDelete(this IEndpointRouteBuilder builder, Delegate handler, [StringSyntax("Route")] string pattern)
        {
            builder.MapDelete(pattern, handler)
                .WithName(handler.Method.Name);

            return builder;
        }
    }

}