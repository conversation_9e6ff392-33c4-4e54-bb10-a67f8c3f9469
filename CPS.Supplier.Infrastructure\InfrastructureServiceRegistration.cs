﻿using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using CPS.Supplier.Application.Common.Configuration;
using CPS.Supplier.Application.Models.Mail;
using CPS.Supplier.Infrastructure.Extensions;
using CPS.Supplier.Infrastructure.Mail;
using CPS.Supplier.Infrastructure.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace CPS.Supplier.Infrastructure
{
    public static class InfrastructureServiceRegistration
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
        {
            ArgumentNullException.ThrowIfNull(configuration);

            // Configuration binding with validation
            services.Configure<EmailSettings>(configuration.GetSection("EmailSettings"));

            // Note: AzureStorageSettings configuration is now handled in ApplicationServiceRegistration
            // This ensures consistent validation across all layers

            // Register Azure clients for DI
            services.AddSingleton<BlobServiceClient>(provider =>
            {
                var options = provider.GetRequiredService<IOptions<AzureStorageSettings>>();
                return new BlobServiceClient(options.Value.ConnectionString);
            });

            services.AddSingleton<QueueServiceClient>(provider =>
            {
                var options = provider.GetRequiredService<IOptions<AzureStorageSettings>>();
                QueueClientOptions queueClientOptions = new() { MessageEncoding = QueueMessageEncoding.Base64 };

                return new QueueServiceClient(options.Value.ConnectionString, queueClientOptions);
            });

            // Infrastructure services
            services.AddTransient<IEmailService, EmailService>();
            services.AddTransient<IAzureStorageService, AzureStorageService>();
            services.AddTransient<IQueueService, QueueService>();

            services.AddApplicationInsightsServices(configuration);

            services.TryAddTransient<ISftpService, SftpService>();

            return services;
        }
    }
}