namespace CPS.Supplier.Application.Services.DataFile.Dto;

public class CsvValidationResult
{
    public bool IsValid { get; set; }
    public ICollection<string> Errors { get; init; } = new List<string>();
    public int TotalRecords { get; set; }
    public ICollection<string> ColumnNames { get; init; } = new List<string>();
    public IDictionary<string, IList<string>> FieldErrors { get; init; } = new Dictionary<string, IList<string>>(StringComparer.Ordinal);
    public char Separator { get; set; } = ',';
}