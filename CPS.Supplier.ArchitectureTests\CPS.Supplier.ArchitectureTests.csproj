﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
  </PropertyGroup>  

  <ItemGroup>
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="NetArchTest.Rules" />
    <PackageReference Include="TngTech.ArchUnitNET.xUnit" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CPS.Supplier.Api\CPS.Supplier.Api.csproj" />
    <ProjectReference Include="..\CPS.Supplier.Application\CPS.Supplier.Application.csproj" />
    <ProjectReference Include="..\CPS.Supplier.Infrastructure\CPS.Supplier.Infrastructure.csproj" />
    <ProjectReference Include="..\CPS.Supplier.Persistence\CPS.Supplier.Persistence.csproj" />
  </ItemGroup>

</Project>
