# Azure Function for Supplier Volume Data Processing

This Azure Function processes supplier volume data files from Azure Blob Storage using the existing SSIS workflow.

## Architecture

The solution follows a clean architecture pattern with the following layers:

- **CPS.Supplier.Functions**: Azure Function entry points
- **CPS.Supplier.Application**: Business logic, services, and database operations
- **CPS.Supplier.Persistence**: Data context and EF Core configuration
- **CPS.Supplier.Infrastructure**: External service integrations

## Components

### 1. Azure Function
- **SupplierFileQueueTrigger**: Processes messages from `my-queue-name`
- **Function Name**: `ProcessSupplierFile`
- **Trigger**: Azure Storage Queue

### 2. Application Services
- **ISupplierFileService**: Orchestrates the entire file processing workflow and handles all database operations
- **IAzureStorageService**: Downloads files from Azure Blob Storage, uploads to archive, and deletes processed files
- **IFtpService**: Uploads files to FTP server using FluentFTP library

### 3. Processing Flow
1. **Queue Trigger** → Download from blob → Save to temp file → Upload to SFTP
2. **Execute SPL_UploadFile** → Triggers SupplierFileUpload.dtsx
3. **Check Validation Errors** → Critical errors stop processing
4. **Execute SPL_FileListScreen_ApproveFileByID** → Triggers GenerateApprovedOutputFile.dtsx
5. **Execute SPL_SSIS_MoveFilesToVtrak** → Triggers MoveFilesToVtrak.dtsx

## Configuration

### local.settings.json
```json
{
    "IsEncrypted": false,
    "Values": {
        "AzureWebJobsStorage": "UseDevelopmentStorage=true",
        "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
        "ApplicationInsights:ConnectionString": "",
        "SFTP:Host": "**********",
        "SFTP:Port": "22",
        "SFTP:Username": "sftpuser",
        "SFTP:Password": "sftppassword",
        "SFTP:BasePath": "/SupplierPortal/DEV/InputVolumeFiles/",
        "ConnectionStrings:SupplierConnectionString": "Server=yourserver;Database=SupplierDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;"
    }
}
```

### host.json
```json
{
    "version": "2.0",
    "functionTimeout": "00:10:00",
    "extensions": {
        "queues": {
            "maxPollingInterval": "00:00:02",
            "visibilityTimeout": "00:01:00",
            "batchSize": 1,
            "maxDequeueCount": 3
        }
    }
}
```

## SFTP Configuration

The SFTP service uses the SSH.NET library with the following configuration options:

- **SFTP:Host**: SFTP server address (default: **********)
- **SFTP:Port**: SFTP server port (default: 22)
- **SFTP:Username**: SFTP username for authentication
- **SFTP:Password**: SFTP password for authentication
- **SFTP:BasePath**: Base path for supplier files (default: /supplier-files)

### SFTP Features
- **Secure file transfer**: Uses SSH protocol for encrypted file transfers
- **Directory creation**: Creates destination directories if they don't exist
- **File operations**: Upload and download operations

## Queue Message Format

```json
{
    "fileName": "supplier_data_20240101.csv",
    "blobPath": "uploads/supplier_data_20240101.csv",
    "containerName": "supplier-uploads",
    "processingMonth": "202401",
    "userSK": 27035,
    "supplierId": 123,
    "correlationId": "12345678-1234-1234-1234-123456789012"
}
```

## Required Fields

- **fileName**: Name of the file to process
- **blobPath**: Path to the file in blob storage
- **containerName**: Azure Blob Storage container name
- **processingMonth**: Month in YYYYMM format
- **userSK**: User identifier that must exist in SPL.CPSDB_CPSUser table
- **supplierId**: Supplier ID for processing

## Error Handling

### Centralized Exception Handling
The application relies on centralized exception handling mechanisms:
- Minimal try-catch blocks in service methods
- Try-catch blocks only where necessary (e.g., temporary file cleanup)
- Let exceptions bubble up to be handled by the framework's exception handling

### Supplier Volume Error Types
The system uses a comprehensive enum-based error classification system:

```csharp
public enum SupplierVolumeErrorType
{
    ColumnsDoNotMatch = 1,           // Columns do not match
    MismatchInDataType = 2,          // Mismatch in the data-type
    CharacterLimitExceeded = 3,      // Character limit exceeded
    MandatoryFieldMissing = 4,       // A mandatory field is missing
    SqlError = 5,                    // SQL Error
    DuplicateCustomerRecord = 6,     // Duplicate customer record for the same transaction month
    DataOlderThan12Months = 7,       // File contains data older than 12 months
    InvalidRecordNumber = 8,         // Invalid Record Number
    CurrentOrFutureMonthData = 9,    // File contains data for either the current month or a future month
    InvalidProvince = 10,            // Invalid Province
    FileHasNoData = 11,              // File has no data
    InvalidDateFormat = 12,          // Invalid date format; it should be in the MM/YYYY or MM/DD/YYYY format
    DuplicateRecordNumber = 13,      // Duplicate record number
    InvalidCustomerId = 14,          // Invalid Customer ID
    InvalidAmount = 15               // Invalid Amount
}
```

### Critical Error Types
The following error types will stop processing (defined in `Constants.SupplierVolumeFileProcessing.CriticalErrorTypes`):

- **Type 1 (ColumnsDoNotMatch)**: Columns do not match
- **Type 4 (MandatoryFieldMissing)**: A mandatory field is missing
- **Type 5 (SqlError)**: SQL Error
- **Type 6 (DuplicateCustomerRecord)**: Duplicate customer record for the same transaction month
- **Type 9 (CurrentOrFutureMonthData)**: File contains data for either the current month or a future month
- **Type 13 (DuplicateRecordNumber)**: Duplicate record number

### Non-Critical Errors
All other error types allow processing to continue but are logged for review:

- **Type 2**: Mismatch in the data-type
- **Type 3**: Character limit exceeded
- **Type 7**: File contains data older than 12 months
- **Type 8**: Invalid Record Number
- **Type 10**: Invalid Province
- **Type 11**: File has no data
- **Type 12**: Invalid date format
- **Type 14**: Invalid Customer ID
- **Type 15**: Invalid Amount

## Processing Statuses

- **FILE_UPLOADED**: File successfully uploaded and processed by SSIS
- **VALIDATION_FAILED**: File contains critical validation errors
- **FILE_APPROVED**: File approved and ready for integration
- **PROCESSING_COMPLETE**: All processing steps completed successfully
- **PROCESSING_FAILED**: Unexpected error occurred during processing

## Development Setup

1. **Prerequisites**:
   - .NET 8.0 SDK
   - Azure Storage Emulator or Azure Storage Account
   - SQL Server with supplier database
   - SFTP server access

2. **Configuration**:
   - Update `local.settings.json` with your connection strings
   - Ensure database connection is properly configured
   - Set up SFTP credentials using the SFTP: configuration format

3. **Running Locally**:
   ```bash
   cd CPS.Supplier.Functions
   func start
   ```

## Deployment

1. **Azure Resources Required**:
   - Azure Function App (.NET 8)
   - Azure Storage Account (for queues and blobs)
   - Application Insights (optional but recommended)

2. **Configuration**:
   - Set all connection strings in Function App configuration
   - Configure managed identity for secure access to storage accounts
   - Set up application insights for monitoring
   - Configure SFTP settings using the SFTP: prefix

## Libraries Used

- **SSH.NET**: SSH and SFTP client library for .NET
- **Azure.Storage.Blobs**: Azure Blob Storage SDK
- **Microsoft.Azure.Functions.Worker**: Azure Functions v4 worker
- **Microsoft.EntityFrameworkCore**: Entity Framework Core for database operations

## Architecture Changes

### Simplified Service Structure
The architecture has been simplified to maintain consistency:

- **Current Structure**: All operations are consolidated in `SupplierFileService`
- **Database Operations**: Directly integrated into the main service file
- **Exception Handling**: Minimal try-catch blocks, relying on centralized exception handling
- **Error Classification**: Enum-based error types with constants for critical errors

### Service Responsibilities
- **SupplierFileService**: 
  - Orchestrates the complete file processing workflow
  - Handles all database operations (stored procedures, queries)
  - Manages file operations (blob download, SFTP upload)
  - Coordinates temporary file cleanup
- **BlobService**: Manages Azure Blob Storage operations
- **SftpService**: Manages SFTP operations using SSH.NET

### Exception Handling Strategy
- **Centralized Approach**: Let exceptions bubble up to framework handling
- **Minimal Try-Catch**: Only used where necessary (e.g., cleanup operations)
- **Consistent Behavior**: Predictable error handling across all operations

### Error Type Management
- **Enum-Based**: Uses `SupplierVolumeErrorType` enum for type-safe error classification
- **Constants**: Critical error types defined in `Constants.SupplierVolumeFileProcessing`
- **Maintainable**: Easy to add new error types or modify critical error classification

## Monitoring

The function includes comprehensive logging at all levels:
- Information logs for normal processing steps
- Warning logs for validation errors
- Error logs for exceptions and failures
- Debug logs for temporary file cleanup

All logs include correlation IDs for tracking specific file processing requests.

## Security

- Use managed identity for Azure Storage access in production
- Store sensitive configuration in Azure Key Vault
- Implement proper authentication for SFTP access
- Use SSL/TLS for all external communications
- Temporary files are automatically cleaned up after processing

## Troubleshooting

### Common Issues

1. **Queue messages not processing**:
   - Check queue connection string
   - Verify queue exists and has messages
   - Review function logs for errors

2. **Database connection issues**:
   - Verify connection string format
   - Check database server accessibility
   - Ensure user has proper permissions

3. **SFTP upload failures**:
   - Verify SFTP server connectivity (ping **********)
- Check credentials and permissions
- Review SFTP logs for detailed error information
- Ensure SFTP:BasePath exists on the server

4. **Blob download failures**:
   - Verify blob storage connection string
   - Check container and blob existence
   - Verify managed identity permissions

5. **Temporary file issues**:
   - Check system temp directory permissions
   - Monitor disk space availability
   - Review cleanup logs for any issues

6. **Validation error issues**:
   - Review the specific error type from the `SupplierVolumeErrorType` enum
   - Check if the error is critical (stops processing) or non-critical (allows continuation)
   - Verify data format matches expected schema

## Support

For issues or questions:
1. Check function logs in Application Insights
2. Review queue messages for proper format
3. Verify all configuration settings
4. Check database connectivity and permissions
5. Test SFTP connectivity separately if needed
6. Review validation errors using the error type enum for specific guidance 