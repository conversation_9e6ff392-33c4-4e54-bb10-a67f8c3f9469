﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierVolumeFileDatumConfiguration : IEntityTypeConfiguration<SupplierVolumeFileDatum>
    {
        public void Configure(EntityTypeBuilder<SupplierVolumeFileDatum> entity)
        {
            entity.HasKey(e => e.IntDataFileRowId)
                .IsClustered(false)
                .HasAnnotation("SqlServer:FillFactor", 80);

            entity.ToTable("SupplierVolumeFileData", "SPL");

            entity.Property(e => e.IntDataFileRowId).HasColumnName("intDataFileRowID");
            entity.Property(e => e.DecTotalVolume)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("decTotalVolume");
            entity.Property(e => e.DttmTransactionMonth)
                .HasColumnType("datetime")
                .HasColumnName("dttmTransactionMonth");
            entity.Property(e => e.IntDataFileId).HasColumnName("intDataFileID");
            entity.Property(e => e.IntDataStatusId).HasColumnName("intDataStatusID");
            entity.Property(e => e.IntRecordNo).HasColumnName("intRecordNo");
            entity.Property(e => e.IntUserSk).HasColumnName("intUserSK");
            entity.Property(e => e.StrAracpscustomerId)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("strARACPSCustomerID");
            entity.Property(e => e.StrCustFileCity)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("strCustFileCity");
            entity.Property(e => e.StrCustFileLocation)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("strCustFileLocation");
            entity.Property(e => e.StrCustFilePostCode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("strCustFilePostCode");
            entity.Property(e => e.StrCustFileProvinceCode)
                .HasMaxLength(5)
                .IsUnicode(false)
                .HasColumnName("strCustFileProvinceCode");
            entity.Property(e => e.StrCustFileStaddress1)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strCustFileSTAddress1");
            entity.Property(e => e.StrCustFileStaddress2)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strCustFileSTAddress2");
            entity.Property(e => e.StrCustFileTitle)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strCustFileTitle");
            entity.Property(e => e.StrSupplierCustomerId)
                .IsRequired()
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("strSupplierCustomerID");

            entity.HasOne(d => d.IntDataStatus).WithMany(p => p.SupplierVolumeFileData)
                .HasForeignKey(d => d.IntDataStatusId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SupplierVolumeFileData_SupplierVolumeDataStatus");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierVolumeFileDatum> entity);
    }
}
