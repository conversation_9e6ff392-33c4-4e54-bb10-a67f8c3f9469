namespace CPS.Supplier.Application.Models.AdministrationApi;

public class AdministrationApiSettings
{
    public const string SectionName = "AdministrationApi";
    public string BaseUrl { get; set; } = string.Empty;
    public Auth0Settings Auth0 { get; set; } = new();
    public MasterDataSettings MasterData { get; set; } = new();
    public UserSettings User { get; set; } = new();
    public PasswordResetSettings PasswordReset { get; set; } = new();
    public PollySettings Polly { get; set; } = new();
}

public class Auth0Settings
{
    public string TokenUrl { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    public string M2MAudience { get; set; } = string.Empty;
}

public class MasterDataSettings
{
    public string Language { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string AddressType { get; set; } = string.Empty;
    public string PhoneType { get; set; } = string.Empty;
    public string Countries { get; set; } = string.Empty;
    public string Provinces { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string CountryCodes { get; set; } = string.Empty;
}

public class UserSettings
{
    public string GetUserDetailId { get; set; } = string.Empty;
    public string CreateSupplierUser { get; set; } = string.Empty;
    public string GetEditUser { get; set; } = string.Empty;
    public string UpdateUser { get; set; } = string.Empty;
    public string UpdateSupplierUserStatus { get; set; } = string.Empty;
    public string GetPasswordPolicy { get; set; } = string.Empty;
    public string EmailExists { get; set; } = string.Empty;
    public string UsernameExists { get; set; } = string.Empty;
}

public class PasswordResetSettings
{
    public string Request { get; set; } = string.Empty;
    public string Reset { get; set; } = string.Empty;
    public string Validate { get; set; } = string.Empty;
}

public class PollySettings
{
    public int RetryCount { get; set; } = 3;
    public double BaseDelaySeconds { get; set; } = 1.0;
    public int TimeoutSeconds { get; set; } = 30;
    public bool UseJitter { get; set; } = true;
}