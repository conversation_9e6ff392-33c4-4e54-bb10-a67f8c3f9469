namespace CPS.Supplier.Application.Services.DataFile.Dto;

/// <summary>
/// Data required for Azure Function to process uploaded files
/// </summary>
public class FileProcessingQueueDto
{
    /// <summary>
    /// Database primary key for file record created during upload
    /// </summary>
    public int FileId { get; set; }

    /// <summary>
    /// Supplier identifier for security validation
    /// </summary>
    public int SupplierId { get; set; }

    /// <summary>
    /// User who uploaded the file
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// Full Azure blob URL for file download
    /// </summary>
    public string BlobUrl { get; set; } = string.Empty;
}