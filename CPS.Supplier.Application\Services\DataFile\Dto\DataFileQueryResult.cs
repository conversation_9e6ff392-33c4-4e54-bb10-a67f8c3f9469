namespace CPS.Supplier.Application.Services.DataFile.Dto;

/// <summary>
/// Internal DTO for database query results when retrieving data files
/// </summary>
internal sealed class DataFileQueryResult
{
    public int FileId { get; set; }
    public string SupplierName { get; set; } = string.Empty;
    public string Month { get; set; } = string.Empty;
    public string Year { get; set; } = string.Empty;
    public decimal TotalVolume { get; set; }
    public int TotalRecords { get; set; }
    public int UnmappedRecords { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool HasMultipleMonths { get; set; }
    public DateTime UploadDate { get; set; }
    public bool CanApprove { get; set; }
}