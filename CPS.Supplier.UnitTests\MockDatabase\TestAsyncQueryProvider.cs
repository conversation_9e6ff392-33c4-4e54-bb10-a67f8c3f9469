﻿using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Query;

namespace CPS.Supplier.UnitTests.MockDatabase
{
    public class TestAsyncQueryProvider<TEntity>(IQueryProvider inner) : IAsyncQueryProvider
    {
        public IQueryable CreateQuery(Expression expression)
        {
            return new TestAsyncEnumerable<TEntity>(expression);
        }

        public IQueryable<TElement> CreateQuery<TElement>(Expression expression)
        {
            return new TestAsyncEnumerable<TElement>(expression);
        }

        public object Execute(Expression expression)
        {
            return inner.Execute(expression)!;
        }

        public TResult Execute<TResult>(Expression expression)
        {
            return inner.Execute<TResult>(expression);
        }

        public TResult ExecuteAsync<TResult>(Expression expression, CancellationToken cancellationToken = default)
        {
            var resultType = typeof(TResult).GetGenericArguments()[0];

            // Using null-conditional and null-forgiving operators instead of disabling warnings
            var method = typeof(IQueryProvider)
                .GetMethod(
                    name: nameof(IQueryProvider.Execute),
                    genericParameterCount: 1,
                    types: [typeof(Expression)]) ?? throw new InvalidOperationException("Could not find Execute method on IQueryProvider");
            var genericMethod = method.MakeGenericMethod(resultType);
            var executionResult = genericMethod.Invoke(inner, [expression]);

            var taskFromResultMethod = typeof(Task).GetMethod(nameof(Task.FromResult))
                ?.MakeGenericMethod(resultType);

            return taskFromResultMethod == null
                ? throw new InvalidOperationException("Could not find FromResult method on Task")
                : (TResult)taskFromResultMethod.Invoke(null, [executionResult])!;
        }
    }
}