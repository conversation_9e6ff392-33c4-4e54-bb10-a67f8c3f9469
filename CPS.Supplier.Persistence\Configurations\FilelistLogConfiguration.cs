﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class FilelistLogConfiguration : IEntityTypeConfiguration<FilelistLog>
    {
        public void Configure(EntityTypeBuilder<FilelistLog> entity)
        {
            entity
                .HasNoKey()
                .ToTable("FilelistLog", "SPL");

            entity.Property(e => e.IntUserSk)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("intUserSk");
            entity.Property(e => e.StrMonth)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strMonth");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<FilelistLog> entity);
    }
}
