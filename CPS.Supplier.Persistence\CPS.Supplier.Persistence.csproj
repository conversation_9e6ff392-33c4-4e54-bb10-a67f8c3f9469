﻿<Project Sdk="Microsoft.NET.Sdk">
  
  <ItemGroup>
    <PackageReference Include="Meziantou.Analyzer">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />   
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CPS.Supplier.Application\CPS.Supplier.Application.csproj" />
  </ItemGroup>

</Project>