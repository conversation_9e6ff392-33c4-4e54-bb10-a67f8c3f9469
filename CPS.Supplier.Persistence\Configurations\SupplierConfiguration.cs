﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierConfiguration : IEntityTypeConfiguration<Domain.Entities.Supplier>
    {
        public void Configure(EntityTypeBuilder<Domain.Entities.Supplier> entity)
        {
            entity
                .HasNoKey()
                .ToView("Supplier", "SPL");

            entity.Property(e => e.IntStatusId).HasColumnName("intStatusID");
            entity.Property(e => e.IntSupplierId).HasColumnName("intSupplierID");
            entity.Property(e => e.SrcVendorId)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("srcVendorId");
            entity.Property(e => e.StrMsanumber)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("strMSANumber");
            entity.Property(e => e.StrName)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("strName");
            entity.Property(e => e.StrStatus)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("strStatus");
            entity.Property(e => e.UserCount).HasColumnName("UserCount");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<Domain.Entities.Supplier> entity);
    }
}
