using System.Collections.ObjectModel;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Common.Configuration;
using CPS.Supplier.Application.Services.DataFile.Dto;

namespace CPS.Supplier.Infrastructure.Services;

public class AzureStorageService(
    IOptions<AzureStorageSettings> options,
    BlobServiceClient blobServiceClient,
    ILogger<AzureStorageService> logger) : IAzureStorageService
{
    private readonly AzureStorageSettings _options = options.Value;

    public async Task<Stream> DownloadFileAsync(string fileName, string containerName, CancellationToken cancellationToken = default)
    {
        var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
        var blobClient = containerClient.GetBlobClient(fileName);
        var response = await blobClient.DownloadStreamingAsync(cancellationToken: cancellationToken);
        return response.Value.Content;
    }

    public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string containerName, IReadOnlyDictionary<string, string> metadata, CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Uploading file to blob storage. FileName: {FileName}", fileName);

        var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
        await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);

        var blobClient = containerClient.GetBlobClient(fileName);
        fileStream.Position = 0;

        await blobClient.UploadAsync(fileStream, overwrite: true, cancellationToken: cancellationToken);
        await blobClient.SetMetadataAsync(metadata.ToDictionary(kvp => kvp.Key, kvp => kvp.Value, StringComparer.OrdinalIgnoreCase), cancellationToken: cancellationToken);

        logger.LogInformation("Successfully uploaded file to blob storage. FileName: {FileName}", fileName);
        return blobClient.Uri.ToString();
    }

    public async Task<ICollection<BlobFileInfo>> GetProcessingFilesAsync(int? supplierId, string? processingMonth = null, DateTime? cutoffDate = null, CancellationToken cancellationToken = default)
    {
        var containerClient = blobServiceClient.GetBlobContainerClient(_options.BlobContainerName);
        var result = new List<BlobFileInfo>();

        // Use BlobTraits.Metadata to get metadata in single call for better performance
        // Files are uploaded to root container, so we enumerate all blobs and filter by metadata
        await foreach (var blobItem in containerClient.GetBlobsAsync(
            traits: BlobTraits.Metadata,
            cancellationToken: cancellationToken))
        {
            // Apply cutoff date filter early if specified
            if (cutoffDate.HasValue && blobItem.Properties.LastModified.HasValue &&
                blobItem.Properties.LastModified.Value.DateTime < cutoffDate.Value)
            {
                continue;
            }

            // Filter by supplier if specified
            if (supplierId.HasValue &&
                (!blobItem.Metadata.TryGetValue(BlobMetadata.MetadataSupplierId, out var metadataSupplierId) ||
                 !int.TryParse(metadataSupplierId, System.Globalization.CultureInfo.InvariantCulture, out var blobSupplierId) ||
                 blobSupplierId != supplierId.Value))
            {
                continue;
            }

            // Apply processing month filter if specified
            if (processingMonth != null &&
                (!blobItem.Metadata.TryGetValue(BlobMetadata.MetadataProcessingMonth, out var metadataMonth) ||
                 !string.Equals(metadataMonth, processingMonth, StringComparison.Ordinal)))
            {
                continue;
            }

            result.Add(new BlobFileInfo
            {
                Name = blobItem.Name,
                Metadata = new ReadOnlyDictionary<string, string>(blobItem.Metadata),
                LastModified = blobItem.Properties.LastModified ?? DateTimeOffset.UtcNow,
                Size = blobItem.Properties.ContentLength ?? 0
            });
        }

        return result;
    }

    public async Task<bool> HasExistingUploadAsync(int supplierId, string processingMonth, CancellationToken cancellationToken = default)
    {
        var containerClient = blobServiceClient.GetBlobContainerClient(_options.BlobContainerName);

        // Use BlobTraits.Metadata to get metadata in single call for better performance
        // Files are uploaded to root container, so we enumerate all blobs and filter by metadata
        await foreach (var blobItem in containerClient.GetBlobsAsync(
            traits: BlobTraits.Metadata,
            cancellationToken: cancellationToken))
        {
            // Check if blob belongs to the specified supplier and has the correct processing month
            if (blobItem.Metadata.TryGetValue(BlobMetadata.MetadataSupplierId, out var metadataSupplierId) &&
                int.TryParse(metadataSupplierId, System.Globalization.CultureInfo.InvariantCulture, out var blobSupplierId) &&
                blobSupplierId == supplierId &&
                blobItem.Metadata.TryGetValue(BlobMetadata.MetadataProcessingMonth, out var metadataMonth) &&
                string.Equals(metadataMonth, processingMonth, StringComparison.Ordinal))
            {
                return true;
            }
        }

        return false;
    }
    public async Task<bool> DeleteFileAsync(string fileName, string containerName, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Deleting file from blob storage. FileName: {FileName}, Container: {Container}", fileName, containerName);

            var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
            var blobClient = containerClient.GetBlobClient(fileName);
            var response = await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);

            logger.LogInformation("Successfully deleted file from blob storage. FileName: {FileName}, Deleted: {Deleted}", fileName, response.Value);
            return response.Value;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to delete file from blob storage. FileName: {FileName}", fileName);
            throw;
        }
    }


}