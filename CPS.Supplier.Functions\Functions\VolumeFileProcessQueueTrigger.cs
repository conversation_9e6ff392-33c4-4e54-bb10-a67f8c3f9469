using System.Text.Json;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.VolumeFileProcess.Contracts;
using CPS.Supplier.Application.Services.VolumeFileProcess.Model;
using CPS.Supplier.Functions.Extensions;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;

namespace CPS.Supplier.Functions.Functions;

public class VolumeFileProcessQueueTrigger(
    ILogger<VolumeFileProcessQueueTrigger> logger,
    IVolumeFileProcessService supplierFileService,
    IMetricsService metricsService)
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true
    };

    [Function("ProcessSupplierFile")]
    public async Task Run([QueueTrigger("%AzureStorage:QueueName%", Connection = "AzureStorage:ConnectionString")] string queueMessage, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Azure Function triggered with message: {QueueMessage}", queueMessage);

            if (string.IsNullOrWhiteSpace(queueMessage))
            {
                logger.LogWarning("Received empty queue message");
                return;
            }

            var fileMessage = DeserializeMessage(queueMessage);
            if (fileMessage == null) return;

            await ProcessFileMessage(fileMessage, cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Critical error in Azure Function before message processing. Message: {ErrorMessage}, StackTrace: {StackTrace}",
                ex.Message, ex.StackTrace);
            throw;
        }
    }

    private FileUploadMessage? DeserializeMessage(string queueMessage)
    {
        try
        {
            logger.LogInformation("Attempting to deserialize message: {QueueMessage}", queueMessage);
            var fileMessage = JsonSerializer.Deserialize<FileUploadMessage>(queueMessage, JsonOptions);
            logger.LogInformation("Successfully deserialized message. CorrelationId: {CorrelationId}, FileName: {FileName}",
                fileMessage?.CorrelationId, fileMessage?.FileName);
            return fileMessage;
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Invalid JSON in queue message: {Message}", queueMessage);
            return null;
        }
    }

    private async Task ProcessFileMessage(FileUploadMessage fileMessage, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Processing supplier file queue message. CorrelationId: {CorrelationId}", fileMessage.CorrelationId);

            var result = await supplierFileService.ProcessSupplierFileAsync(fileMessage, cancellationToken);

            if (result.Success)
            {
                LogSuccessfulProcessing(result);
            }
            else
            {
                LogFailedProcessing(result);
            }
        }
        catch (Exception ex)
        {
            var correlationId = fileMessage?.CorrelationId ?? "Unknown";
            logger.LogError(ex, "Unhandled exception in ProcessSupplierFile. CorrelationId: {CorrelationId}, Message: {ErrorMessage}",
                correlationId, ex.Message);
            logger.HandleExceptionAsync(metricsService, ex, "ProcessSupplierFile", correlationId);
            throw;
        }
    }

    private void LogSuccessfulProcessing(FileProcessingResult result)
    {
        logger.LogInformation("Successfully processed supplier file. CorrelationId: {CorrelationId}, DataFileId: {DataFileId}, Status: {Status}",
            result.CorrelationId, result.DataFileId, result.ProcessingStatus);
    }

    private void LogFailedProcessing(FileProcessingResult result)
    {
        logger.LogWarning("Supplier file processing failed. CorrelationId: {CorrelationId}, Status: {Status}, Error: {Error}",
            result.CorrelationId, result.ProcessingStatus, result.ErrorMessage);

        if (result.ValidationErrors.Any())
        {
            foreach (var error in result.ValidationErrors)
            {
                logger.LogWarning("Validation error - Type: {ErrorType}, Line: {Line}, Column: {Column}, Description: {Description}",
                    error.ErrorTypeId, error.LineNumber, error.ColumnName, error.ErrorDescription);
            }
        }

        logger.LogInformation("Message processing completed with business logic failure - not retrying");
    }
}