﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>

namespace CPS.Supplier.Persistence.Configurations
{
    public partial class VwSupplierUserConfiguration : IEntityTypeConfiguration<VwSupplierUser>
    {
        public void Configure(EntityTypeBuilder<VwSupplierUser> entity)
        {
            entity
                .HasNoKey()
                .ToView("vw_SupplierUsers", "SPL");

            entity.Property(e => e.IntLanguageId).HasColumnName("intLanguageID");
            entity.Property(e => e.IntSupplierId).HasColumnName("intSupplierID");
            entity.Property(e => e.IntUserSk).HasColumnName("intUserSK");
            entity.Property(e => e.StrCode)
                .HasMaxLength(20)
                .IsFixedLength()
                .HasColumnName("strCode");
            entity.Property(e => e.StrEmailAddress)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strEmailAddress");
            entity.Property(e => e.StrFirstName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strFirstName");
            entity.Property(e => e.StrLastName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strLastName");
            entity.Property(e => e.StrUsername)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strUsername");
            entity.Property(e => e.SupplierName)
                .IsRequired()
                .HasMaxLength(33)
                .IsUnicode(false);
            entity.Property(e => e.IntStatusId).HasColumnName("intStatusID");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<VwSupplierUser> entity);
    }
}
