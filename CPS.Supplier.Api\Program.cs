using CPS.Supplier.Infrastructure.Extensions;
using Serilog;

try
{
    var builder = WebApplication.CreateBuilder(args);

    builder.Configuration
        .SetBasePath(Directory.GetCurrentDirectory())
        .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
        .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
        .AddEnvironmentVariables()
        .AddCommandLine(args);

    // Add KeyVault configuration for non-development environments
    builder.Configuration.AddKeyVaultConfiguration(builder.Environment);

    Log.Logger = new LoggerConfiguration()
        .WriteTo.Console(formatProvider: System.Globalization.CultureInfo.InvariantCulture)
        .CreateBootstrapLogger();

    Log.Information("Configuring web host ({ApplicationName})...", builder.Environment.ApplicationName);

    builder.Host.ConfigureLogging();

    var app = builder
        .ConfigureServices()
        .ConfigurePipeline();

    Log.Information("Starting web host ({ApplicationName})...", builder.Environment.ApplicationName);

    await app.RunAsync();
}

finally
{
    await Log.CloseAndFlushAsync();
}

public partial class Program
{
    protected Program()
    {
    }
}