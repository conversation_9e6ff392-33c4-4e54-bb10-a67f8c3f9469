﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierVolumeFileStatusConfiguration : IEntityTypeConfiguration<SupplierVolumeFileStatus>
    {
        public void Configure(EntityTypeBuilder<SupplierVolumeFileStatus> entity)
        {
            entity.HasKey(e => e.IntFileStatusId).IsClustered(false);

            entity.ToTable("SupplierVolumeFileStatus", "SPL");

            entity.Property(e => e.IntFileStatusId).HasColumnName("intFileStatusID");
            entity.Property(e => e.StrName)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strName");
            entity.Property(e => e.StrNameFr)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strNameFR");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierVolumeFileStatus> entity);
    }
}
