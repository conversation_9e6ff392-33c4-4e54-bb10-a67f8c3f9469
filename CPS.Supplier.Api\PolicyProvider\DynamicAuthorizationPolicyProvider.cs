﻿using CPS.Supplier.Application.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;

namespace CPS.Supplier.Api.PolicyProvider;
public class DynamicAuthorizationPolicyProvider : DefaultAuthorizationPolicyProvider
{
    private readonly ILogger<DynamicAuthorizationPolicyProvider> _logger;
    public DynamicAuthorizationPolicyProvider(
        IOptions<AuthorizationOptions> options,
        ILogger<DynamicAuthorizationPolicyProvider> logger) : base(options)
    {
        _logger = logger;
    }

    public override async Task<AuthorizationPolicy?> GetPolicyAsync(string policyName)
    {
        if (string.IsNullOrEmpty(policyName))
        {
            return await base.GetPolicyAsync(policyName);
        }

        var parts = policyName.Split(Delimiters.ModulePermissionSeparator);
        if (parts.Length == 2)
        {
            var module = parts[0];
            var permission = parts[1];

            _logger.LogDebug("Creating dynamic policy for {Module}.{Permission}", module, permission);

            var policy = new AuthorizationPolicyBuilder()
                .RequireAuthenticatedUser()
                .AddRequirements(new ModulePermissionRequirement(module, permission))
                .Build();

            return policy;
        }

        if (policyName.Contains(Delimiters.MultiplePolicyDelimiter, StringComparison.Ordinal))
        {
            var policyNames = policyName.Split(Delimiters.MultiplePolicyDelimiter, StringSplitOptions.RemoveEmptyEntries)
                .Select(p => p.Trim())
                .ToArray();

            _logger.LogDebug("Creating OR policy for: {Policies}", string.Join(", ", policyNames));

            var permissions = new List<(string Module, string Permission)>();

            foreach (var individualPolicyName in policyNames)
            {
                parts = individualPolicyName.Split(Delimiters.ModulePermissionSeparator);
                if (parts.Length == 2)
                {
                    var module = parts[0];
                    var permission = parts[1];
                    permissions.Add((module, permission));
                }
            }

            if (permissions.Count == 0)
            {
                return null;
            }

            var policy = new AuthorizationPolicyBuilder()
                .RequireAuthenticatedUser()
                .AddRequirements(new OrModulePermissionRequirement(permissions))
                .Build();

            return policy;
        }

        _logger.LogDebug("Falling back to base policy provider for: {PolicyName}", policyName);
        return await base.GetPolicyAsync(policyName);
    }
}