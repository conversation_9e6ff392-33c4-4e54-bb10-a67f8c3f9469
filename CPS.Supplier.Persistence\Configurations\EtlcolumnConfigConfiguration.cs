﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class EtlcolumnConfigConfiguration : IEntityTypeConfiguration<EtlcolumnConfig>
    {
        public void Configure(EntityTypeBuilder<EtlcolumnConfig> entity)
        {
            entity
                .HasNoKey()
                .ToTable("ETLColumnConfig", "SPL");

            entity.Property(e => e.IntColumnId)
                .ValueGeneratedOnAdd()
                .HasColumnName("intColumnID");
            entity.Property(e => e.IntSortOrder).HasColumnName("intSortOrder");
            entity.Property(e => e.StrColumnNameEn)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("strColumnNameEN");
            entity.Property(e => e.StrColumnNameFr)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("strColumnNameFR");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<EtlcolumnConfig> entity);
    }
}
