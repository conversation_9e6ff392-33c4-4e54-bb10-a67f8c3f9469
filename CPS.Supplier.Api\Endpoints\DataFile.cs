using CPS.Supplier.Api.Infrastructure;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.DataFile.Commands;
using CPS.Supplier.Application.Services.DataFile.Contracts;
using CPS.Supplier.Application.Services.DataFile.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;

namespace CPS.Supplier.Api.Endpoints
{
    public class DataFile : IEndpointGroupBase
    {
        public void Map(WebApplication app)
        {
            var group = app.MapGroup(this)
                .RequireAuthorization();

            // File operations endpoints
            group.MapGet(GetDataFiles, "/");
            group.MapPost(UploadDataFile, "/upload");
            group.MapGet(CanUploadNewFile, "/suppliers/{supplierId:int}/can-upload");
            group.MapGet(GetFileStatus, "/{fileId:int}/status");
            group.MapGet(GetFileErrorSummary, "/{fileId:int}/errors");

            // File lifecycle management endpoints
            group.MapPut(ApproveFile, "/{fileId:int}/approve");
            group.MapPut(RejectFile, "/{fileId:int}/reject");
            group.MapPut(CompleteFile, "/{fileId:int}/complete");
            group.MapDelete(DeleteFile, "/{fileId:int}");

            // Record operations endpoints
            group.MapGet(GetFileRecords, "/{fileId:int}/records");
            group.MapGet(GetRecordMappingInfo, "/records/{recordId:int}/mapping");

            // Record mapping management endpoints
            group.MapPut(MapRecord, "/records/{recordId:int}/map");
            group.MapPut(UnmapRecord, "/records/{recordId:int}/unmap");
            group.MapPut(MarkRecordAsInvalid, "/records/{recordId:int}/mark-invalid");
            group.MapPut(MarkRecordAsValid, "/records/{recordId:int}/mark-valid");

            // Customer matching endpoints
            group.MapGet(GetPotentialCustomerMatches, "/records/{recordId:int}/potential-matches");
        }

        private static async Task<Ok<IReadOnlyList<DataFileDto>>> GetDataFiles(
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.get-data-files.Count", 1);
            var result = await dataFileService.GetDataFilesAsync(ct);
            return TypedResults.Ok(result);
        }

        private static async Task<Results<Ok<FileRecordDto>, NotFound>> GetFileRecords(
            int fileId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.get-file-records.Count", 1);
            var result = await dataFileService.GetFileRecordsAsync(fileId, ct);
            return TypedResults.Ok(result);
        }

        private static async Task<Results<Ok<RecordMappingDto>, NotFound>> GetRecordMappingInfo(
            int recordId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.get-record-mapping-info.Count", 1);
            var result = await dataFileService.GetRecordMappingInfoAsync(recordId, ct);
            return TypedResults.Ok(result);
        }

        private static async Task<Results<Ok<FileErrorSummaryDto>, NotFound>> GetFileErrorSummary(
            int fileId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.get-file-error-summary.Count", 1);
            var result = await dataFileService.GetFileErrorSummaryAsync(fileId, ct);
            return TypedResults.Ok(result);
        }

        [Authorize(Policy = Policies.UploadFile)]
        private static async Task<Results<NoContent, BadRequest>> UploadDataFile(
            HttpContext httpContext,
            IDataFileService dataFileService,
            IMetricsService metricsService)
        {
            metricsService.TrackMetric("DataFileApi.upload-data-file.Count", 1);
            var command = await ParseUploadFormAsync(httpContext);
            await dataFileService.UploadFileAsync(command);
            return TypedResults.NoContent();
        }

        private static async Task<Results<NoContent, NotFound, BadRequest>> MapRecord(
            int recordId,
            MapRecordCommand command,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.map-record.Count", 1);
            await dataFileService.MapRecordAsync(recordId, command, ct);
            return TypedResults.NoContent();
        }

        private static async Task<Results<NoContent, NotFound, BadRequest>> UnmapRecord(
            int recordId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.unmap-record.Count", 1);
            await dataFileService.UnmapRecordAsync(recordId, ct);
            return TypedResults.NoContent();
        }

        private static async Task<Results<NoContent, NotFound, BadRequest>> MarkRecordAsInvalid(
            int recordId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.mark-record-invalid.Count", 1);
            await dataFileService.MarkRecordAsInvalidAsync(recordId, ct);
            return TypedResults.NoContent();
        }

        private static async Task<Results<NoContent, NotFound, BadRequest>> MarkRecordAsValid(
            int recordId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.mark-record-valid.Count", 1);
            await dataFileService.MarkRecordAsValidAsync(recordId, ct);
            return TypedResults.NoContent();
        }

        private static async Task<Results<NoContent, NotFound>> CompleteFile(
            int fileId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.complete-file.Count", 1);
            await dataFileService.CompleteFileAsync(fileId, ct);
            return TypedResults.NoContent();
        }

        private static async Task<Ok<bool>> CanUploadNewFile(
            int supplierId,
            IDataFileService dataFileService,
            IMetricsService metricsService)
        {
            metricsService.TrackMetric("DataFileApi.can-upload-new-file.Count", 1);
            var canUpload = await dataFileService.CanUploadNewFileAsync(supplierId);
            return TypedResults.Ok(canUpload);
        }

        [Authorize(Policy = Policies.ApproveRejectAramarkAudit)]
        private static async Task<Results<NoContent, NotFound, BadRequest>> ApproveFile(
            int fileId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.approve-file.Count", 1);
            await dataFileService.ApproveFileAsync(fileId, ct);
            return TypedResults.NoContent();
        }


        private static async Task<Results<Ok<FileStatusResponse>, NotFound>> GetFileStatus(
            int fileId,
            IDataFileService dataFileService,
            IMetricsService metricsService)
        {
            metricsService.TrackMetric("DataFileApi.get-file-status.Count", 1);
            var result = await dataFileService.GetFileStatusAsync(fileId);
            return TypedResults.Ok(result);
        }

        [Authorize(Policy = Policies.ApproveRejectAramarkAudit)]
        private static async Task<Results<NoContent, NotFound, BadRequest>> RejectFile(
            int fileId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.reject-file.Count", 1);
            await dataFileService.RejectFileAsync(fileId, ct);
            return TypedResults.NoContent();
        }

        private static async Task<Results<NoContent, NotFound>> DeleteFile(
            int fileId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.delete-file.Count", 1);
            await dataFileService.DeleteFileAsync(fileId, ct);
            return TypedResults.NoContent();
        }

        private static async Task<Results<Ok<IEnumerable<CustomerMatchResultDto>>, NotFound>> GetPotentialCustomerMatches(
            int recordId,
            IDataFileService dataFileService,
            IMetricsService metricsService,
            CancellationToken ct)
        {
            metricsService.TrackMetric("DataFileApi.get-potential-customer-matches.Count", 1);
            var result = await dataFileService.GetPotentialCustomerMatchesAsync(
                recordId,
                ct);

            return TypedResults.Ok(result);

        }

        // Helper method for form parsing
        private static async Task<UploadFileCommand> ParseUploadFormAsync(HttpContext httpContext)
        {
            var form = await httpContext.Request.ReadFormAsync();
            var file = form.Files.GetFile("file");
            var supplierIdStr = form["supplierId"].FirstOrDefault();

            int? supplierId = null;
            if (!string.IsNullOrEmpty(supplierIdStr) &&
                int.TryParse(supplierIdStr, System.Globalization.CultureInfo.InvariantCulture, out var parsedSupplierId))
            {
                supplierId = parsedSupplierId;
            }

            return new UploadFileCommand
            {
                File = file!,
                SupplierId = supplierId
            };
        }
    }
}