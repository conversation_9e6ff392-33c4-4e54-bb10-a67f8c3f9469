using CPS.Supplier.Application.Common;
using Microsoft.Extensions.Logging;

namespace CPS.Supplier.Functions.Extensions
{
    public static class FunctionExceptionHandlerExtensions
    {
        public static void HandleExceptionAsync(
            this ILogger logger,
            IMetricsService metricsService,
            Exception exception,
            string functionName,
            string correlationId)
        {
            var exceptionType = exception.GetType().Name;
            var metricName = $"FunctionExceptionHandler.{exceptionType}.Count";

            logger.LogError(exception, "Function exception: {FunctionName}, Type: {ExceptionType}, CorrelationId: {CorrelationId}",
                functionName, exceptionType, correlationId);

            metricsService.TrackMetric(metricName, 1);
            metricsService.TrackEvent($"FunctionExceptionHandler.{exceptionType}", new Dictionary<string, string>(StringComparer.Ordinal)
            {
                ["Message"] = exception.Message,
                ["FunctionName"] = functionName,
                ["CorrelationId"] = correlationId,
                ["ExceptionType"] = exceptionType
            });
        }
    }
}