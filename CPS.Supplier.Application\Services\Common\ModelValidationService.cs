﻿using System.Text.Json;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Application.Services.Common.Dto;
using FluentValidation.Results;
using Microsoft.Extensions.DependencyInjection;

namespace CPS.Supplier.Application.Services.Common
{
    public class ModelValidationService(IServiceProvider serviceProvider) : IModelValidationService
    {
        public void Validate<T>(T model)
        {
            var validator = serviceProvider.GetRequiredService<IValidator<T>>();
            var validationResult = validator.Validate(model);

            if (!validationResult.IsValid)
            {
                FormatExceptionResult(validationResult);
            }
        }

        public async Task ValidateAsync<T>(T model, CancellationToken cancellationToken = default)
        {
            var validator = serviceProvider.GetRequiredService<IValidator<T>>();
            var validationResult = await validator.ValidateAsync(model, cancellationToken);

            if (!validationResult.IsValid)
            {
                FormatExceptionResult(validationResult);
            }
        }

        private static void FormatExceptionResult(ValidationResult validationResult)
        {
            List<ErrorDetailDto> validationErrors = [];

            foreach (var validationError in validationResult.Errors)
            {
                validationErrors.Add(new ErrorDetailDto { Field = validationError.PropertyName, Message = validationError.ErrorMessage });
            }

            throw new ValidationException(JsonSerializer.Serialize(validationErrors));
        }
    }
}