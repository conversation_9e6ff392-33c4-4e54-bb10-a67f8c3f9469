using CPS.Supplier.Application.Services.MasterData.Dto;

namespace CPS.Supplier.Application.Services.MasterData.Contracts;

public interface IMasterDataService
{
    Task<IEnumerable<LanguageDto>?> GetCompanyLanguages(int companyId, CancellationToken cancellationToken = default);
    Task<IEnumerable<StatusDto>?> GetStatuses(CancellationToken cancellationToken = default);
    Task<IEnumerable<AddressTypeDto>?> GetAddressTypes(CancellationToken cancellationToken = default);
    Task<IEnumerable<PhoneTypeDto>?> GetPhoneTypes(CancellationToken cancellationToken = default);
    Task<IEnumerable<CountryDto>?> GetCountries(CancellationToken cancellationToken = default);
    Task<IEnumerable<ProvinceDto>?> GetProvinces(int? countryId, CancellationToken cancellationToken);
    Task<IEnumerable<RoleDto>?> GetAllRoles(CancellationToken cancellationToken = default);
    Task<IEnumerable<CountryCodeDto>?> GetCountryCodes(CancellationToken cancellationToken = default);
}