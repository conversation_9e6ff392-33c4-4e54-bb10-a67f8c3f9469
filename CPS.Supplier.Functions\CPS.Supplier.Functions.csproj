﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Meziantou.Analyzer">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />

        <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" />
        <PackageReference Include="Azure.Security.KeyVault.Secrets" />
        <PackageReference Include="Azure.Storage.Blobs" />
        <PackageReference Include="Microsoft.Extensions.Configuration" />
        <PackageReference Include="SSH.NET" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Storage.Queues" />
        <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" />
        <PackageReference Include="Microsoft.Extensions.Localization" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\CPS.Supplier.Application\CPS.Supplier.Application.csproj" />
      <ProjectReference Include="..\CPS.Supplier.Persistence\CPS.Supplier.Persistence.csproj" />
      <ProjectReference Include="..\CPS.Supplier.Infrastructure\CPS.Supplier.Infrastructure.csproj" />
    </ItemGroup>
    <ItemGroup>
      <None Update="appsettings.Development.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.Integration.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.Qa.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.Development.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.Production.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="local.settings.json.dist">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>Never</CopyToPublishDirectory>
      </None>
    </ItemGroup>
</Project>