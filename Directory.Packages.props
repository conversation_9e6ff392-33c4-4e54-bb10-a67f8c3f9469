<!-- For more info on central package management go to https://devblogs.microsoft.com/nuget/introducing-central-package-management/ -->
<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="AspNetCore.HealthChecks.SqlServer" Version="9.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageVersion Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
    <PackageVersion Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
    <PackageVersion Include="Azure.Storage.Blobs" Version="12.24.1" />
    <PackageVersion Include="Azure.Storage.Queues" Version="12.22.0" />
    <PackageVersion Include="ClosedXML" Version="0.102.2" />
    <PackageVersion Include="SSH.NET" Version="2024.2.0" />
    <PackageVersion Include="FluentValidation" Version="11.11.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageVersion Include="FuzzySharp" Version="2.0.2" />
    <PackageVersion Include="Meziantou.Analyzer" Version="2.0.188" />
    <PackageVersion Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageVersion Include="Microsoft.ApplicationInsights.WorkerService" Version="2.23.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.Abstractions" Version="2.3.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.15" />
    <PackageVersion Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.0.15" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.15" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="8.0.15" />
    <PackageVersion Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
    <PackageVersion Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="2.0.0" />
    <PackageVersion Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.1" />
    <PackageVersion Include="Microsoft.Azure.Functions.Worker.Extensions.Storage.Queues" Version="5.5.0" />
    <PackageVersion Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.2" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.3" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.6" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.3" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.3" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.24" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.6" />
    <PackageVersion Include="Microsoft.Extensions.Http.Polly" Version="8.0.15" />
    <PackageVersion Include="Microsoft.Extensions.Localization" Version="8.0.15" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.3" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageVersion Include="MockQueryable.NSubstitute" Version="7.0.3" />
    <PackageVersion Include="NetArchTest.Rules" Version="1.3.2" />
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <PackageVersion Include="Polly" Version="7.2.4" />
    <PackageVersion Include="SendGrid" Version="9.29.3" />
    <PackageVersion Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageVersion Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Shouldly" Version="4.3.0" />
    <PackageVersion Include="SonarAnalyzer.CSharp" Version="10.7.0.110445" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="7.3.1" />
    <PackageVersion Include="Testcontainers.MsSql" Version="4.3.0" />
    <PackageVersion Include="TngTech.ArchUnitNET.xUnit" Version="0.11.3" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.4.3" />
  </ItemGroup>
</Project>