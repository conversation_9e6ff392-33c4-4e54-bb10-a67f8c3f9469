﻿using Microsoft.AspNetCore.Authorization;

namespace CPS.Supplier.Api.PolicyProvider;

public class OrModulePermissionRequirement : IAuthorizationRequirement
{
    public IReadOnlyList<(string Module, string Permission)> Permissions { get; }

    public OrModulePermissionRequirement(IEnumerable<(string Module, string Permission)> permissions)
    {
        Permissions = permissions.ToList().AsReadOnly();
    }
}