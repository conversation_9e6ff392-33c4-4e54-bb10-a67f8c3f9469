namespace CPS.Supplier.Application.Common;

/// <summary>
/// Defines the status values for individual data records within supplier volume files.
/// Replaces DataStatusConstants to provide type safety and better maintainability.
/// </summary>
[System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1027:Mark enums with FlagsAttribute", Justification = "This enum represents mutually exclusive states, not combinable flags")]
public enum DataStatus
{
    Uploaded = 1,
    Unmapped = 2,
    ReadyToProcess = 3,
    Mapped = 5,
    ModifiedMapping = 6,
    InvalidRecord = 8,
    NewMapping = 9
}

/// <summary>
/// Business rule groupings for data status values.
/// </summary>
public static class DataStatusRules
{
    /// <summary>
    /// Status values that indicate unmapped records requiring attention.
    /// </summary>
    public static readonly DataStatus[] UnmappedStatuses = [DataStatus.Unmapped, DataStatus.InvalidRecord];
}