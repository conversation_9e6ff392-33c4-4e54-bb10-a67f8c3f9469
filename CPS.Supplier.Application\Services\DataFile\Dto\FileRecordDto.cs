namespace CPS.Supplier.Application.Services.DataFile.Dto
{
    public class FileRecordDto
    {
        public IEnumerable<RecordDto> Records { get; set; } = Enumerable.Empty<RecordDto>();
        public string SupplierName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Month { get; set; } = string.Empty;
        public string Year { get; set; } = string.Empty;
    }
}