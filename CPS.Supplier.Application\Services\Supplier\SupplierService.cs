using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.Supplier.Contracts;
using CPS.Supplier.Application.Services.Supplier.Dto;
using CPS.Supplier.Application.Services.UserContext.Contracts;
using CPS.Supplier.Domain.Entities;

namespace CPS.Supplier.Application.Services.Supplier;

public class SupplierService : ISupplierService
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public SupplierService(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<IReadOnlyList<SupplierDto>> GetSuppliersWithUserCount(CancellationToken ct)
    {
        // Only internal users will call this method (external users don't see Manage Users section)
        var suppliers = await _context.GetDbSet<Domain.Entities.Supplier>()
                        .Where(s => s.IntStatusId == SupplierConstants.ActiveStatusId)
                        .Select(s => new SupplierDto
                        {
                            SupplierId = s.IntSupplierId,
                            Name = s.StrName,
                            Status = s.StrStatus,
                            UserCount = s.UserCount,
                            VendorId = s.SrcVendorId
                        })
                        .OrderBy(s => s.Name)
                        .ToListAsync(ct);

        return suppliers.AsReadOnly();
    }

    public async Task<IReadOnlyList<SupplierUserDto>> GetSupplierUsers(int supplierId, CancellationToken ct)
    {
        var supplierUsers = await _context.GetDbSet<VwSupplierUser>()
            .Where(x => x.IntSupplierId == supplierId && x.IntLanguageId == _currentUserService.LanguageId)
            .Select(x => new SupplierUserDto
            {
                IntUserSk = x.IntUserSk,
                FirstName = x.StrFirstName,
                LastName = x.StrLastName,
                Username = x.StrUsername,
                Email = x.StrEmailAddress,
                Status = x.StrCode.Trim(),
            })
            .ToListAsync(ct);

        return supplierUsers;
    }
}