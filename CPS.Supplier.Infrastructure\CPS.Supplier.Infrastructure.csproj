﻿<Project Sdk="Microsoft.NET.Sdk">

    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" />
        <PackageReference Include="Azure.Storage.Queues" />
        <PackageReference Include="Meziantou.Analyzer">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Data.SqlClient" />
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />
        <PackageReference Include="SendGrid" />
        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
        <PackageReference Include="Serilog.AspNetCore" />
        <PackageReference Include="Serilog.Sinks.ApplicationInsights" />
        <PackageReference Include="Serilog.Sinks.Console" />
        <PackageReference Include="SSH.NET" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\CPS.Supplier.Application\CPS.Supplier.Application.csproj" />
    </ItemGroup>

</Project>