﻿using CPS.Supplier.Application.Models.AdministrationApi;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Application.Services.PasswordReset.Commands;
using CPS.Supplier.Application.Services.PasswordReset.Contracts;
using CPS.Supplier.Application.Services.PasswordReset.Dto;
using Microsoft.Extensions.Options;

namespace CPS.Supplier.Application.Services.PasswordReset;

public class PasswordResetService : IPasswordResetService
{
    private readonly IHttpClientService _httpClientService;
    private readonly AdministrationApiSettings _apiSettings;

    public PasswordResetService(
        IHttpClientService httpClientService,
        IOptions<AdministrationApiSettings> settings)
    {
        _httpClientService = httpClientService;
        _apiSettings = settings.Value;
    }

    public async Task<PasswordResetRequestResponseDto> RequestPasswordResetAsync(PasswordResetRequestCommand command, CancellationToken cancellationToken)
    {
        var endpoint = _apiSettings.PasswordReset.Request;
        var result = await _httpClientService.PostAsync<PasswordResetRequestCommand, PasswordResetRequestResponseDto>(endpoint, command, cancellationToken, allowAnonymous: true);
        return result ?? new PasswordResetRequestResponseDto();
    }

    public async Task<PasswordResetResponseDto> ResetPasswordAsync(PasswordResetCommand command, CancellationToken cancellationToken)
    {
        var endpoint = _apiSettings.PasswordReset.Reset;
        var result = await _httpClientService.PostAsync<PasswordResetCommand, PasswordResetResponseDto>(endpoint, command, cancellationToken, allowAnonymous: true);
        return result ?? new PasswordResetResponseDto();
    }

    public async Task<ValidatePasswordResetTokenResponseDto> ValidatePasswordResetTokenAsync(ValidatePasswordResetTokenCommand command, CancellationToken cancellationToken)
    {
        var endpoint = _apiSettings.PasswordReset.Validate;
        var result = await _httpClientService.PostAsync<ValidatePasswordResetTokenCommand, ValidatePasswordResetTokenResponseDto>(endpoint, command, cancellationToken, allowAnonymous: true);
        return result ?? ValidatePasswordResetTokenResponseDto.Failed("Validation failed");
    }
}