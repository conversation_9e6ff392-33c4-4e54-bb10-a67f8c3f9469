using CPS.Supplier.Application.Models.AdministrationApi;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Application.Services.MasterData;
using CPS.Supplier.Application.Services.MasterData.Dto;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute.ExceptionExtensions;

namespace CPS.Supplier.UnitTests
{
    public class MasterDataServiceTests
    {
        private readonly IHttpClientService _httpClientService;
        private readonly IOptions<AdministrationApiSettings> _settings;
        private readonly ILogger<MasterDataService> _logger;
        private readonly MasterDataService _service;
        private readonly AdministrationApiSettings _apiSettings;
        public MasterDataServiceTests()
        {
            // Setup mocks
            _httpClientService = Substitute.For<IHttpClientService>();
            _logger = Substitute.For<ILogger<MasterDataService>>();

            // Setup configuration
            _apiSettings = new AdministrationApiSettings
            {
                MasterData = new MasterDataSettings
                {
                    Language = "/api/MasterData/company-languages",
                    Status = "/api/MasterData/status",
                    AddressType = "/api/MasterData/address-types",
                    PhoneType = "/api/MasterData/phone-types"
                }
            };

            _settings = Substitute.For<IOptions<AdministrationApiSettings>>();
            _settings.Value.Returns(_apiSettings);

            // Create the actual service (not a mock)
            _service = new MasterDataService(_httpClientService, _settings, _logger);
        }

        [Fact]
        public async Task GetCompanyLanguagesShouldReturnLanguagesWhenSuccessful()
        {
            // Arrange
            var companyId = 123;


            var expectedLanguages = new List<LanguageDto>
            {
                new() { LanguageId = 1, Name = "English" },
                new() { LanguageId = 2, Name = "Spanish" },
                new() { LanguageId = 3, Name = "French" }
            };

            var expectedEndpoint = "/api/MasterData/company-languages?companyId=123";
            _httpClientService.GetAsync<IEnumerable<LanguageDto>>(expectedEndpoint, Arg.Any<CancellationToken>())
                .Returns(expectedLanguages);

            // Act
            var result = await _service.GetCompanyLanguages(companyId);

            // Assert
            result.ShouldNotBeNull();
            result.Count().ShouldBe(3);

            var languagesList = result.ToList();
            languagesList[0].LanguageId.ShouldBe(1);
            languagesList[0].Name.ShouldBe("English");

            // Verify the correct endpoint was called
            await _httpClientService.Received(1).GetAsync<IEnumerable<LanguageDto>>(expectedEndpoint, Arg.Any<CancellationToken>());
        }

        [Fact]
        public async Task GetCompanyLanguagesShouldRethrowExceptionWhenHttpClientThrows()
        {
            // Arrange
            var companyId = 123;

            var expectedEndpoint = "/api/MasterData/company-languages?companyId=123";
            var expectedException = new HttpRequestException("Network error");

            _httpClientService.GetAsync<IEnumerable<LanguageDto>>(expectedEndpoint, Arg.Any<CancellationToken>())
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = await Should.ThrowAsync<HttpRequestException>(
                () => _service.GetCompanyLanguages(companyId));

            exception.ShouldBe(expectedException);
        }

        [Fact]
        public async Task GetStatusesShouldReturnStatusesWhenSuccessful()
        {

            // Arrange
            var expectedStatuses = new List<StatusDto>
            {
                new() { StatusTypeId = 1, Name = "Active" },
                new() { StatusTypeId = 2, Name = "Inactive" },
                new() { StatusTypeId = 3, Name = "Pending" }
            };

            var expectedEndpoint = "/api/MasterData/status";
            _httpClientService.GetAsync<IEnumerable<StatusDto>>(expectedEndpoint, Arg.Any<CancellationToken>())
                .Returns(expectedStatuses);

            // Act
            var result = await _service.GetStatuses();

            // Assert
            result.ShouldNotBeNull();
            result.Count().ShouldBe(3);

            var statusesList = result.ToList();
            statusesList[0].StatusTypeId.ShouldBe(1);
            statusesList[0].Name.ShouldBe("Active");
            statusesList[1].StatusTypeId.ShouldBe(2);
            statusesList[1].Name.ShouldBe("Inactive");
            statusesList[2].StatusTypeId.ShouldBe(3);
            statusesList[2].Name.ShouldBe("Pending");

            await _httpClientService.Received(1).GetAsync<IEnumerable<StatusDto>>(expectedEndpoint, Arg.Any<CancellationToken>());
        }

        [Fact]
        public async Task GetAddressTypesShouldReturnAddressTypesWhenSuccessful()
        {
            // Arrange
            var expectedAddressTypes = new List<AddressTypeDto>
            {
                new() { AddressTypeId = 1, Name = "Home" },
                new() { AddressTypeId = 2, Name = "Work" },
                new() { AddressTypeId = 3, Name = "Billing" }
            };

            var expectedEndpoint = "/api/MasterData/address-types";
            _httpClientService.GetAsync<IEnumerable<AddressTypeDto>>(expectedEndpoint, Arg.Any<CancellationToken>())
                .Returns(expectedAddressTypes);

            // Act
            var result = await _service.GetAddressTypes();

            // Assert
            result.ShouldNotBeNull();
            result.Count().ShouldBe(3);

            var addressTypesList = result.ToList();
            addressTypesList[0].AddressTypeId.ShouldBe(1);
            addressTypesList[0].Name.ShouldBe("Home");
            addressTypesList[1].AddressTypeId.ShouldBe(2);
            addressTypesList[1].Name.ShouldBe("Work");
            addressTypesList[2].AddressTypeId.ShouldBe(3);
            addressTypesList[2].Name.ShouldBe("Billing");

            await _httpClientService.Received(1).GetAsync<IEnumerable<AddressTypeDto>>(expectedEndpoint, Arg.Any<CancellationToken>());
        }

        [Fact]
        public async Task GetPhoneTypesShouldReturnPhoneTypesWhenSuccessful()
        {
            // Arrange
            var expectedPhoneTypes = new List<PhoneTypeDto>
            {
                new() { PhoneNumberTypeId = 1, Name = "Mobile" },
                new() { PhoneNumberTypeId = 2, Name = "Home" },
                new() { PhoneNumberTypeId = 3, Name = "Work" }
            };

            var expectedEndpoint = "/api/MasterData/phone-types";
            _httpClientService.GetAsync<IEnumerable<PhoneTypeDto>>(expectedEndpoint, Arg.Any<CancellationToken>())
                .Returns(expectedPhoneTypes);

            // Act
            var result = await _service.GetPhoneTypes();

            // Assert
            result.ShouldNotBeNull();
            result.Count().ShouldBe(3);

            var phoneTypesList = result.ToList();
            phoneTypesList[0].PhoneNumberTypeId.ShouldBe(1);
            phoneTypesList[0].Name.ShouldBe("Mobile");
            phoneTypesList[1].PhoneNumberTypeId.ShouldBe(2);
            phoneTypesList[1].Name.ShouldBe("Home");
            phoneTypesList[2].PhoneNumberTypeId.ShouldBe(3);
            phoneTypesList[2].Name.ShouldBe("Work");

            await _httpClientService.Received(1).GetAsync<IEnumerable<PhoneTypeDto>>(expectedEndpoint, Arg.Any<CancellationToken>());
        }
    }
}