using System.Reflection;
using CPS.Supplier.Application.Interfaces.Persistence;

namespace CPS.Supplier.Persistence.Data
{
    public partial class SupplierDbContext : DbContext, IApplicationDbContext
    {
        private readonly IDatabaseFacade _databaseFacade;

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        public SupplierDbContext() { } // Keep for EF tools
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.

        public SupplierDbContext(DbContextOptions<SupplierDbContext> options)
            : base(options)
        {
            _databaseFacade = CreateDatabaseFacade();
        }

        // Generic method to access any entity DbSet
        public DbSet<TEntity> GetDbSet<TEntity>() where TEntity : class
        {
            return Set<TEntity>();
        }

        IDatabaseFacade IApplicationDbContext.Database => _databaseFacade;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Apply all entity configurations from this assembly
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        }

        private DatabaseFacadeWrapper CreateDatabaseFacade()
        {
            return new DatabaseFacadeWrapper(Database);
        }
    }
}