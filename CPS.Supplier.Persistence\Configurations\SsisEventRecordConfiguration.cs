﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SsisEventRecordConfiguration : IEntityTypeConfiguration<SsisEventRecord>
    {
        public void Configure(EntityTypeBuilder<SsisEventRecord> entity)
        {
            entity
                .HasNoKey()
                .ToTable("SSIS_EventRecord", "SPL");

            entity.Property(e => e.DttmEndEventTime)
                .HasColumnType("datetime")
                .HasColumnName("dttmEndEventTime");
            entity.Property(e => e.DttmEventDate)
                .HasColumnType("datetime")
                .HasColumnName("dttmEventDate");
            entity.Property(e => e.DttmStartEventTime)
                .HasColumnType("datetime")
                .HasColumnName("dttmStartEventTime");
            entity.Property(e => e.ErrorDesc).IsUnicode(false);
            entity.Property(e => e.IntEventRecordId)
                .ValueGeneratedOnAdd()
                .HasColumnName("intEventRecordID");
            entity.Property(e => e.StrApplicationExecutionId)
                .HasMaxLength(256)
                .HasColumnName("strApplicationExecutionID");
            entity.Property(e => e.StrDesc)
                .HasMaxLength(2000)
                .HasColumnName("strDesc");
            entity.Property(e => e.StrPackageName)
                .HasMaxLength(256)
                .HasColumnName("strPackageName");
            entity.Property(e => e.StrStartEnd)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("strStartEnd");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SsisEventRecord> entity);
    }
}
