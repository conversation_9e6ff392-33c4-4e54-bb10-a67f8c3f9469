namespace CPS.Supplier.Application.Services.DataFile.Dto;

public class DataFileDto
{
    public int FileId { get; set; }
    public string SupplierName { get; set; } = string.Empty;
    public decimal TotalVolume { get; set; }
    public int TotalRecords { get; set; }
    public int UnmappedRecords { get; set; }
    public string Status { get; set; } = string.Empty;
    public bool HasMultipleMonths { get; set; }
    public DateTime UploadDate { get; set; }
    public bool CanApprove { get; set; }

    /// <summary>
    /// Collection of months when HasMultipleMonths is true
    /// UI can format as needed (e.g., "Jan 2025 & Jan 2024")
    /// </summary>
    public IEnumerable<FileMonthInfo> Months { get; set; } = [];
}

public class FileMonthInfo
{
    public string? Month { get; set; } = string.Empty;
    public string? Year { get; set; } = string.Empty;
}



public class CustomerInfoDto
{
    public string CustomerDivisionClientId { get; set; } = string.Empty;
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Province { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string Company { get; set; } = string.Empty;
    public DateTime? OpenDate { get; set; }
    public DateTime? ClosedDate { get; set; }
}