using System.Text.Json;
using Azure.Storage.Queues;

namespace CPS.Supplier.Infrastructure.Services;

public class QueueService(
    QueueServiceClient queueServiceClient,
    ILogger<QueueService> logger) : IQueueService
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    public async Task SendMessageAsync<T>(T message, string queueName, CancellationToken cancellationToken = default)
    {
        var queueClient = queueServiceClient.GetQueueClient(queueName);
        await queueClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);

        var messageContent = JsonSerializer.Serialize(message, JsonOptions);
        await queueClient.SendMessageAsync(messageContent, cancellationToken: cancellationToken);
    }

    public async Task<T?> ReceiveMessageAsync<T>(string queueName, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Receiving message from queue: {QueueName}", queueName);

            var queueClient = queueServiceClient.GetQueueClient(queueName);

            var response = await queueClient.ReceiveMessageAsync(cancellationToken: cancellationToken);

            if (response.Value != null)
            {
                var message = JsonSerializer.Deserialize<T>(response.Value.MessageText);

                // Delete the message after processing
                await queueClient.DeleteMessageAsync(response.Value.MessageId, response.Value.PopReceipt, cancellationToken);

                logger.LogInformation("Message received and deleted from queue: {QueueName}", queueName);
                return message;
            }

            return default;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error receiving message from queue: {QueueName}", queueName);
            throw;
        }
    }
}