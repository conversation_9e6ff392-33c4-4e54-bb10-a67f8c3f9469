using Microsoft.Extensions.Configuration;

namespace CPS.Supplier.Application.Common.Configuration.Extensions;

/// <summary>
/// Extension methods for IConfiguration to provide type-safe configuration access
/// </summary>
public static class ConfigurationExtensions
{
    /// <summary>
    /// Gets a required configuration section and binds it to the specified type
    /// </summary>
    /// <typeparam name="T">The type to bind the configuration section to</typeparam>
    /// <param name="configuration">The configuration instance</param>
    /// <param name="sectionName">The name of the configuration section</param>
    /// <returns>The bound configuration object</returns>
    /// <exception cref="InvalidOperationException">Thrown when the section is not found or binding fails</exception>
    public static T GetRequiredSection<T>(this IConfiguration configuration, string sectionName)
        where T : class, new()
    {
        var section = configuration.GetSection(sectionName);
        if (!section.Exists())
            throw new InvalidOperationException($"Configuration section '{sectionName}' not found");

        var result = section.Get<T>();
        return result ?? throw new InvalidOperationException($"Failed to bind configuration section '{sectionName}'");
    }

    /// <summary>
    /// Gets a required connection string
    /// </summary>
    /// <param name="configuration">The configuration instance</param>
    /// <param name="name">The name of the connection string</param>
    /// <returns>The connection string</returns>
    /// <exception cref="InvalidOperationException">Thrown when the connection string is not found or empty</exception>
    public static string GetRequiredConnectionString(this IConfiguration configuration, string name)
    {
        var connectionString = configuration.GetConnectionString(name);
        return string.IsNullOrWhiteSpace(connectionString)
            ? throw new InvalidOperationException($"Connection string '{name}' not found or empty")
            : connectionString;
    }

    /// <summary>
    /// Gets a configuration value with a default fallback
    /// </summary>
    /// <typeparam name="T">The type of the configuration value</typeparam>
    /// <param name="configuration">The configuration instance</param>
    /// <param name="key">The configuration key</param>
    /// <param name="defaultValue">The default value to return if the key is not found</param>
    /// <returns>The configuration value or the default value</returns>
    public static T GetValueOrDefault<T>(this IConfiguration configuration, string key, T defaultValue)
    {
        var value = configuration.GetValue<T>(key);
        return value ?? defaultValue;
    }

    /// <summary>
    /// Checks if a configuration section exists and has values
    /// </summary>
    /// <param name="configuration">The configuration instance</param>
    /// <param name="sectionName">The name of the configuration section</param>
    /// <returns>True if the section exists and has values, false otherwise</returns>
    public static bool HasSection(this IConfiguration configuration, string sectionName)
    {
        var section = configuration.GetSection(sectionName);
        return section.Exists() && section.GetChildren().Any();
    }

    /// <summary>
    /// Gets a configuration value and validates it's not null or empty
    /// </summary>
    /// <param name="configuration">The configuration instance</param>
    /// <param name="key">The configuration key</param>
    /// <returns>The configuration value</returns>
    /// <exception cref="InvalidOperationException">Thrown when the value is null or empty</exception>
    public static string GetRequiredValue(this IConfiguration configuration, string key)
    {
        var value = configuration[key];
        return string.IsNullOrWhiteSpace(value)
            ? throw new InvalidOperationException($"Configuration value '{key}' is required but not found or empty")
            : value;
    }
}