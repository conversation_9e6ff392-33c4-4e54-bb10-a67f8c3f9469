<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Status Labels (for UI-only statuses) -->
  <data name="StatusLabel_Processing" xml:space="preserve">
    <value>Processing</value>
  </data>

  <!-- Status Messages -->
  <data name="StatusMessages_Uploaded" xml:space="preserve">
    <value>File has been uploaded and is queued for processing</value>
  </data>
  <data name="StatusMessages_Processing" xml:space="preserve">
    <value>File is currently being processed</value>
  </data>
  <data name="StatusMessages_ExternalMapping" xml:space="preserve">
    <value>File requires manual mapping review</value>
  </data>
  <data name="StatusMessages_Approved" xml:space="preserve">
    <value>File has been approved and processed</value>
  </data>
  <data name="StatusMessages_Rejected" xml:space="preserve">
    <value>File has been rejected</value>
  </data>
  <data name="StatusMessages_Completed" xml:space="preserve">
    <value>File processing is complete</value>
  </data>
  <data name="StatusMessages_Unknown" xml:space="preserve">
    <value>Status unknown</value>
  </data>
  <data name="StatusMessages_NotFound" xml:space="preserve">
    <value>File not found</value>
  </data>

  
  <!-- Validation Messages -->
  <data name="ValidationMessages_FileRequired" xml:space="preserve">
    <value>File is required</value>
  </data>
  <data name="ValidationMessages_InvalidCsvFormat" xml:space="preserve">
    <value>Invalid CSV format. File must be comma or semicolon separated.</value>
  </data>
  <data name="ValidationMessages_ValidSupplierIdRequired" xml:space="preserve">
    <value>Valid supplier ID is required for external users</value>
  </data>
  <data name="ValidationMessages_SupplierNotFound" xml:space="preserve">
    <value>Supplier not found</value>
  </data>
  <data name="ValidationMessages_SupplierNotActive" xml:space="preserve">
    <value>Supplier is not active</value>
  </data>
  <data name="ValidationMessages_SameMonthUploadExists" xml:space="preserve">
    <value>Only one file can be uploaded per month. Please try again next month.</value>
  </data>

  <!-- File Validation Messages (moved from ResponseMessages) -->
  <data name="ValidationMessages_InvalidFileSize" xml:space="preserve">
    <value>File size exceeds maximum allowed limit</value>
  </data>
  <data name="ValidationMessages_InvalidFileExtension" xml:space="preserve">
    <value>Invalid file extension. Only CSV files are allowed</value>
  </data>
  <data name="ValidationMessages_FileNameTooLong" xml:space="preserve">
    <value>File name should be less than 100 characters</value>
  </data>

  <!-- NEW: VolumeFileProcess Validation Messages -->
  <data name="ValidationMessages_FileNameRequired" xml:space="preserve">
    <value>FileName is required</value>
  </data>
  <data name="ValidationMessages_BlobPathRequired" xml:space="preserve">
    <value>BlobPath is required</value>
  </data>
  <data name="ValidationMessages_ContainerNameRequired" xml:space="preserve">
    <value>ContainerName is required</value>
  </data>
  <data name="ValidationMessages_ProcessingMonthRequiredWithFormat" xml:space="preserve">
    <value>ProcessingMonth is required (format: YYYYMM)</value>
  </data>
  <data name="ValidationMessages_UserSKRequired" xml:space="preserve">
    <value>UserSK is required</value>
  </data>
  <data name="ValidationMessages_SupplierIdRequired" xml:space="preserve">
    <value>SupplierId is required</value>
  </data>
  <data name="ValidationMessages_ProcessingMonthInvalidFormat" xml:space="preserve">
    <value>ProcessingMonth must be in YYYYMM format</value>
  </data>

  <!-- NEW: HttpClient Error Messages -->
  <data name="ErrorMessages_RequestFailed" xml:space="preserve">
    <value>Request failed</value>
  </data>
  <data name="ErrorMessages_ResourceNotFound" xml:space="preserve">
    <value>Resource not found</value>
  </data>
  <data name="ErrorMessages_AccessForbidden" xml:space="preserve">
    <value>Access forbidden</value>
  </data>
  <data name="ErrorMessages_Unauthorized" xml:space="preserve">
    <value>Unauthorized</value>
  </data>

  <!-- NEW: Global Exception Messages -->
  <data name="ErrorMessages_ValidationFailed" xml:space="preserve">
    <value>Validation failed</value>
  </data>

  <!-- NEW: Technical Error Messages -->
  <data name="ErrorMessages_TechnicalError" xml:space="preserve">
    <value>Technical Error - An unexpected issue occurred. Please try again later.
If the problem persists, contact Customer Support.</value>
  </data>

  <!-- NEW: Unexpected Error Messages -->
  <data name="ErrorMessages_UnexpectedError" xml:space="preserve">
    <value>An unexpected issue occurred. Please try again later. If the problem persists, contact Customer Support.</value>
  </data>

  <!-- File Operation Error Messages -->
  <data name="ErrorMessages_InvalidFileStatus" xml:space="preserve">
    <value>File status should be Aramark Audit</value>
  </data>
  <data name="ErrorMessages_UserCreationFailed" xml:space="preserve">
    <value>Failed to create user</value>
  </data>
  <data name="ErrorMessages_UnknownField" xml:space="preserve">
    <value>Unknown Field</value>
  </data>
  <data name="ErrorMessages_InsufficientPermissions" xml:space="preserve">
    <value>Insufficient permissions for this operation</value>
  </data>
  <data name="ErrorMessages_SupplierAccessOnly" xml:space="preserve">
    <value>You can only access files belonging to your supplier</value>
  </data>

  <!-- File Operation Validation Messages -->
  <data name="ValidationMessages_MappingRequired" xml:space="preserve">
    <value>Cannot approve file. All records must be mapped or marked as invalid before approval.</value>
  </data>
  <data name="ValidationMessages_InvalidRecordStatus" xml:space="preserve">
    <value>Record cannot be marked as invalid from its current status</value>
  </data>
  <data name="ValidationMessages_CustomerNotFound" xml:space="preserve">
    <value>Aramark Customer ID '{0}' does not exist</value>
  </data>
  <data name="ValidationMessages_OnlyInvalidRecordsCanBeMarkedValid" xml:space="preserve">
    <value>Only invalid records can be marked as valid</value>
  </data>

  <!-- Complete File Validation Message -->
  <data name="ValidationMessages_CompleteFileRequiresMappedRecords" xml:space="preserve">
    <value>Cannot mark file as complete. All records must be mapped or marked as invalid.</value>
  </data>

</root>
