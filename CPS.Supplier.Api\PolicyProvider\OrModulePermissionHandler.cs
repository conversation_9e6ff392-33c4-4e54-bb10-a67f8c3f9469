﻿using CPS.Supplier.Application.Services.UserContext.Contracts;
using Microsoft.AspNetCore.Authorization;

namespace CPS.Supplier.Api.PolicyProvider;

public class OrModulePermissionHandler : AuthorizationHandler<OrModulePermissionRequirement>
{
    private readonly ICurrentUserService _currentUserService;

    public OrModulePermissionHandler(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService;
    }

    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        OrModulePermissionRequirement requirement)
    {
        if (_currentUserService.IsSuperAdmin)
        {
            context.Succeed(requirement);
            return Task.CompletedTask;
        }

        // Check if user has ANY ONE of the specified permissions (OR logic)
        foreach (var (module, permission) in requirement.Permissions)
        {
            if (_currentUserService.HasPermissionInModule(module, permission))
            {
                context.Succeed(requirement);
                return Task.CompletedTask;
            }
        }

        // If we reach here, user doesn't have any of the required permissions
        return Task.CompletedTask;
    }

}