﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierVolumeFileConfiguration : IEntityTypeConfiguration<SupplierVolumeFile>
    {
        public void Configure(EntityTypeBuilder<SupplierVolumeFile> entity)
        {
            entity.HasKey(e => e.IntDataFileId).IsClustered(false);

            entity.ToTable("SupplierVolumeFile", "SPL");

            entity.Property(e => e.IntDataFileId).HasColumnName("intDataFileID");
            entity.Property(e => e.BlnHasMultipleMonths).HasColumnName("blnHasMultipleMonths");
            entity.Property(e => e.DecTotalVolume)
                .HasColumnType("decimal(10, 2)")
                .HasColumnName("decTotalVolume");
            entity.Property(e => e.DttmUploadDate)
                .HasColumnType("datetime")
                .HasColumnName("dttmUploadDate");
            entity.Property(e => e.IntFileStatusId).HasColumnName("intFileStatusID");
            entity.Property(e => e.IntSupplierId).HasColumnName("intSupplierID");
            entity.Property(e => e.IntTotalRecords).HasColumnName("intTotalRecords");
            entity.Property(e => e.IntUnmappedRecords).HasColumnName("intUnmappedRecords");
            entity.Property(e => e.IntUserSk).HasColumnName("intUserSK");
            entity.Property(e => e.StrFileName)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("strFileName");
            entity.Property(e => e.StrMonth)
                .IsRequired()
                .HasMaxLength(5)
                .IsUnicode(false)
                .HasColumnName("strMonth");
            entity.Property(e => e.StrYear)
                .IsRequired()
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("strYear");

            entity.HasOne(d => d.IntFileStatus).WithMany(p => p.SupplierVolumeFiles)
                .HasForeignKey(d => d.IntFileStatusId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SupplierVolumeFile_SupplierVolumeFileStatus");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierVolumeFile> entity);
    }
}
