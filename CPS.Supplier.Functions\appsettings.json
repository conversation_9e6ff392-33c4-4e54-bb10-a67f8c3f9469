{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Azure.Functions": "Information"}}, "ConnectionStrings": {"SupplierConnectionString": ""}, "ApplicationInsights": {"ConnectionString": ""}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.ApplicationInsights"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "Microsoft.EntityFrameworkCore": "Information", "Microsoft.Azure.Functions": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "SupplierPortal.Functions"}}, "EnabledApplicationInsightsLogger": "true", "EnabledApplicationInsightsMetrics": "true", "KeyVault": {"Enabled": true, "Endpoint": "https://ar-az-est1-d-cps-kv-001.vault.azure.net/"}, "AzureStorage": {"BlobContainerName": "supplier-incoming-files", "QueueName": "supplier-incoming-messages", "ArchivedFilesContainer": "supplier-archived-files"}, "AzureWebJobsStorage": "", "ShowDetailedExceptionMessages": false}