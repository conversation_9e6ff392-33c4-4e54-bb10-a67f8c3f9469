using System.Text;
using System.Text.Json;
using CPS.Supplier.Application.Models.AdministrationApi;
using CPS.Supplier.Application.Services.Auth0.Contracts;
using CPS.Supplier.Application.Services.Auth0.Dto;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CPS.Supplier.Application.Services.Auth0;
public class Auth0TokenService : IAuth0TokenService
{
    private readonly HttpClient _httpClient;
    private readonly AdministrationApiSettings _settings;
    private readonly ILogger<Auth0TokenService> _logger;

    public Auth0TokenService(
        HttpClient httpClient,
        IOptions<AdministrationApiSettings> settings,
        ILogger<Auth0TokenService> logger)
    {
        _httpClient = httpClient;
        _settings = settings.Value;
        _logger = logger;
    }

    public async Task<string> GetAccessTokenAsync()
    {
        try
        {
            var tokenRequest = new Auth0TokenRequest
            {
                ClientId = _settings.Auth0.ClientId ?? throw new InvalidOperationException("AdministrationApi:Auth0:ClientId configuration is missing"),
                ClientSecret = _settings.Auth0.ClientSecret ?? throw new InvalidOperationException("AdministrationApi:Auth0:ClientSecret configuration is missing"),
                Audience = _settings.Auth0.M2MAudience ?? throw new InvalidOperationException("AdministrationApi:Auth0:M2MAudience configuration is missing"),
                GrantType = "client_credentials",
            };

            var tokenUrlString = _settings.Auth0.TokenUrl ?? throw new InvalidOperationException("AdministrationApi:Auth0:TokenUrl configuration is missing");
            var tokenUrl = new Uri(tokenUrlString);

            var jsonContent = JsonSerializer.Serialize(tokenRequest);

            using var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            _logger.LogDebug("Requesting Auth0 access token from {TokenUrl}", tokenUrl);

            var response = await _httpClient.PostAsync(tokenUrl, content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to get Auth0 access token. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"Failed to get Auth0 access token. Status: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<Auth0TokenResponse>(responseContent);

            if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.AccessToken))
            {
                _logger.LogError("Auth0 token response is invalid or missing access token");
                throw new InvalidOperationException("Auth0 token response is invalid or missing access token");
            }

            _logger.LogDebug("Successfully obtained Auth0 access token");
            return tokenResponse.AccessToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting Auth0 access token");
            throw;
        }
    }
}