using CPS.Supplier.Application;
using CPS.Supplier.Functions.Extensions;
using CPS.Supplier.Infrastructure;
using CPS.Supplier.Infrastructure.Extensions;
using CPS.Supplier.Persistence;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

var host = new HostBuilder()
    .ConfigureFunctionsWebApplication(worker =>
    {
        // Configure function worker settings if needed
    })
    .ConfigureAppConfiguration((hostBuilderContext, appConfigBuilder) =>
    {
        var env = hostBuilderContext.HostingEnvironment;

        appConfigBuilder
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables();

        // Add KeyVault configuration for non-development environments
        appConfigBuilder.AddKeyVaultConfiguration(env);
    })
    .ConfigureServices((hostBuilderContext, services) =>
    {
        var configuration = hostBuilderContext.Configuration;
        var environment = hostBuilderContext.HostingEnvironment;

        services.AddLocalization();

        // Add Key Vault services
        services.AddKeyVaultServices(configuration, environment);

        // Add Application Insights services for metrics
        services.AddApplicationInsightsServices(configuration);

        // Register service layers
        services.AddApplicationServices(configuration);
        services.AddInfrastructureServices(configuration);
        services.AddPersistenceServices(configuration);
    })
    .ConfigureLogging()
    .Build();

await host.RunAsync();