using System.Collections.ObjectModel;

namespace CPS.Supplier.Application.Services.UserContext.Dto;

public class UserContextDto
{
    public int UserId { get; set; }
    public string Username { get; set; } = string.Empty;

    public ICollection<string> Roles { get; } = new Collection<string>();

    public IDictionary<string, List<string>> ModulePermissions { get; } = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);

    public int? SupplierId { get; set; }
    public string? SupplierName { get; set; }
    public int? FacilityId { get; set; }
    public int LanguageId { get; set; }
    public bool IsSuperAdmin { get; set; }

    public IDictionary<string, string> AdditionalClaims { get; } = new Dictionary<string, string>(StringComparer.Ordinal);
}