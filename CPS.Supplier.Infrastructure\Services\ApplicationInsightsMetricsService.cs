﻿using CPS.Supplier.Application.Common;

namespace CPS.Supplier.Infrastructure.Services;
public class ApplicationInsightsMetricsService : IMetricsService
{
    private readonly Microsoft.ApplicationInsights.TelemetryClient _telemetryClient;
    public ApplicationInsightsMetricsService(Microsoft.ApplicationInsights.TelemetryClient telemetryClient)
    {
        _telemetryClient = telemetryClient;
    }

    private static Dictionary<string, string> AddGlobalTags(IDictionary<string, string>? properties)
    {
        var globalTags = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "unknown" },
            { "serviceName", "CPS.Supplier.API" }
            // Add more global tags here if needed
        };
        if (properties != null)
        {
            foreach (var kvp in properties)
                globalTags[kvp.Key] = kvp.Value;
        }
        return globalTags;
    }

    public void TrackEvent(string eventName, IDictionary<string, string>? properties = null, IDictionary<string, double>? metrics = null)
        => _telemetryClient.TrackEvent(eventName, AddGlobalTags(properties), metrics);
    public void TrackMetric(string name, double value, IDictionary<string, string>? properties = null)
        => _telemetryClient.TrackMetric(name, value, AddGlobalTags(properties));
}