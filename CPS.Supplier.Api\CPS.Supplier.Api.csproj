﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  
  <ItemGroup>
    <Content Remove="appsettings.Integration.json" />
    <Content Remove="appsettings.Production.json" />
    <Content Remove="appsettings.QA.json" />
  </ItemGroup>
  
  <ItemGroup>
    <None Include="appsettings.Qa.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="appsettings.Production.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="appsettings.Integration.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  
  <ItemGroup>
      <PackageReference Include="AspNetCore.HealthChecks.SqlServer" />
      <PackageReference Include="AspNetCore.HealthChecks.UI.Client" />
      <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" />
      <PackageReference Include="Azure.Security.KeyVault.Secrets" />
      <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
      <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" />
      <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" />
      <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" />
      <PackageReference Include="Serilog.AspNetCore" />
      <PackageReference Include="Serilog.Sinks.ApplicationInsights" />
      <PackageReference Include="Serilog.Sinks.Console" />
      <PackageReference Include="Microsoft.Extensions.Http.Polly" />
    <PackageReference Include="Meziantou.Analyzer">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="SSH.NET" />    
    <PackageReference Include="Swashbuckle.AspNetCore" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CPS.Supplier.Application\CPS.Supplier.Application.csproj" />
    <ProjectReference Include="..\CPS.Supplier.Infrastructure\CPS.Supplier.Infrastructure.csproj" />
    <ProjectReference Include="..\CPS.Supplier.Persistence\CPS.Supplier.Persistence.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>