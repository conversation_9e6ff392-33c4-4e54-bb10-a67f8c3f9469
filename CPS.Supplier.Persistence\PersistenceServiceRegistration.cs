﻿using CPS.Supplier.Application.Interfaces.Persistence;
using CPS.Supplier.Persistence.Data;
using CPS.Supplier.Persistence.Interceptors;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CPS.Supplier.Persistence
{
    public static class PersistenceServiceRegistration
    {
        public static IServiceCollection AddPersistenceServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<QueryPerformanceInterceptor>();

            services.AddDbContext<SupplierDbContext>((serviceProvider, options) =>
            {
                var interceptor = serviceProvider.GetRequiredService<QueryPerformanceInterceptor>();
                options.UseSqlServer(configuration.GetConnectionString("SupplierConnectionString"))
                       .AddInterceptors(interceptor);
            });

            services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<SupplierDbContext>());

            return services;
        }
    }
}