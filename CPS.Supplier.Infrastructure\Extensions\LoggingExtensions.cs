﻿using System.Globalization;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Infrastructure.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;

namespace CPS.Supplier.Infrastructure.Extensions;
public static class LoggingExtensions
{
    public static IServiceCollection AddApplicationInsightsServices(this IServiceCollection services, IConfiguration configuration)
    {
        var enabledAppInsightsMetrics = configuration.GetValue<bool>("EnabledApplicationInsightsMetrics");
        var connectionString = configuration["ApplicationInsights:ConnectionString"];
        var hasConnectionString = !string.IsNullOrWhiteSpace(connectionString);

        if (enabledAppInsightsMetrics && hasConnectionString)
        {
            services.AddApplicationInsightsTelemetry(options =>
            {
                options.ConnectionString = connectionString;
            });
        }

        // Metrics abstraction registration
        if (enabledAppInsightsMetrics && hasConnectionString)
        {
            services.AddSingleton<IMetricsService, ApplicationInsightsMetricsService>();
        }
        else
        {
            services.AddSingleton<IMetricsService, NoOpMetricsService>();
        }

        return services;
    }

    public static IHostBuilder ConfigureLogging(this IHostBuilder builder)
    {
        return builder.UseSerilog((context, services, loggerConfiguration) =>
        {
            loggerConfiguration
                .ReadFrom.Configuration(context.Configuration)
                .Enrich.FromLogContext();

            if (context.HostingEnvironment.IsDevelopment())
            {
                loggerConfiguration.WriteTo.Console(formatProvider: CultureInfo.InvariantCulture);
            }

            var enabledAppInsightsLogger = context.Configuration.GetValue<bool>("EnabledApplicationInsightsLogger");
            var appInsightsConnectionString = context.Configuration["ApplicationInsights:ConnectionString"];
            var hasConnectionString = !string.IsNullOrWhiteSpace(appInsightsConnectionString);

            if (enabledAppInsightsLogger && hasConnectionString)
            {
                loggerConfiguration.WriteTo.ApplicationInsights(
                    appInsightsConnectionString,
                    TelemetryConverter.Traces
                );
            }
        });
    }
}