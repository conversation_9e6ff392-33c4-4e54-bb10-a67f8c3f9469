trigger:
  branches:
    include:
      - develop
      - qa
      - sqa

pool:
  name: 'CPSAPP'

variables:
  dotnetVersion: '8.x'
  buildConfiguration: 'Release'
  webProject: 'CPS.Supplier.Api/CPS.Supplier.Api.csproj'
  functionProject: 'CPS.Supplier.Functions/CPS.Supplier.Functions.csproj'
  devFunctionAppName: 'ar-az-est1-d-supplier-functions'
  devWebAppName: 'ar-az-est1-d-cpssupplier-web-001'
  qaFunctionAppName: 'ar-az-est1-q-supplier-functions'
  qaWebAppName: 'ar-az-est1-q-cpssupplier-web-001'
  sqaWebAppName: 'ar-az-est1-sqa-cpssupplier-web-001'
  sqaFunctionAppName: 'ar-az-est1-sqa-supplier-functions'

stages:
- stage: BuildFunctionAndWeb
  displayName: 'Build & Package Web and Function Apps'
  jobs:
    - job: BuildWeb
      displayName: 'Build Web App'
      steps:
        - task: UseDotNet@2
          displayName: 'Use .NET SDK $(dotnetVersion)'
          inputs:
            packageType: sdk
            version: '$(dotnetVersion)'

        - task: DotNetCoreCLI@2
          displayName: 'Restore Web Project'
          inputs:
            command: restore
            projects: '$(webProject)'

        - task: DotNetCoreCLI@2
          displayName: 'Build Web Project'
          inputs:
            command: build
            projects: '$(webProject)'
            arguments: '--configuration $(buildConfiguration)'

        - task: DotNetCoreCLI@2
          displayName: 'Test Web Project'
          inputs:
            command: test
            projects: '$(webProject)'
            arguments: '--configuration $(buildConfiguration)'

        - task: DotNetCoreCLI@2
          displayName: 'Publish Web Project'
          inputs:
            command: publish
            projects: '$(webProject)'
            arguments: '--configuration $(buildConfiguration) --output "$(Build.ArtifactStagingDirectory)\webapp"'
            zipAfterPublish: true

        - task: PublishBuildArtifacts@1
          displayName: 'Publish Web Artifact'
          inputs:
            pathtoPublish: '$(Build.ArtifactStagingDirectory)/webapp'
            artifactName: 'webapp'
            publishLocation: Container

    - job: BuildFunction
      displayName: 'Build Function App'
      steps:
        - task: UseDotNet@2
          displayName: 'Use .NET SDK $(dotnetVersion)'
          inputs:
            packageType: sdk
            version: '$(dotnetVersion)'

        - task: DotNetCoreCLI@2
          displayName: 'Restore Function Project'
          inputs:
            command: restore
            projects: '$(functionProject)'

        - task: DotNetCoreCLI@2
          displayName: 'Build Function Project'
          inputs:
            command: build
            projects: '$(functionProject)'
            arguments: '--configuration $(buildConfiguration)'

        - task: DotNetCoreCLI@2
          displayName: 'Publish Function Project'
          inputs:
            command: publish
            projects: '$(functionProject)'
            arguments: '--configuration $(buildConfiguration) --output "$(Build.ArtifactStagingDirectory)\functionapp"'
            zipAfterPublish: false
            publishWebProjects: false
            modifyOutputPath: false

        - task: ArchiveFiles@2
          displayName: 'Archive Function App Output'
          inputs:
            rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/functionapp'
            includeRootFolder: false
            archiveFile: '$(Build.ArtifactStagingDirectory)/CPS.Supplier.Functions.$(Build.BuildId).zip'
            replaceExistingArchive: true

        - task: PublishBuildArtifacts@1
          displayName: 'Publish Function Artifact'
          inputs:
            pathtoPublish: '$(Build.ArtifactStagingDirectory)/CPS.Supplier.Functions.$(Build.BuildId).zip'
            artifactName: 'functionapp-artifact'
            publishLocation: Container


- stage: DeployDev
  displayName: 'Deploy to Dev'
  dependsOn: BuildFunctionAndWeb
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
  jobs:
    - deployment: DeployFunctionApp
      displayName: 'Deploy Function and Web App'
      environment: dev
      strategy:
        runOnce:
          deploy:
            steps:
              - task: DownloadBuildArtifacts@0
                displayName: 'Download Function ZIP Artifact'
                inputs:
                  buildType: current
                  artifactName: functionapp-artifact
                  downloadPath: '$(Pipeline.Workspace)'

              - task: AzureFunctionApp@1
                displayName: 'Deploy Function App to Azure'
                inputs:
                  azureSubscription: 'AzureDevops-Pipelines'
                  appName: $(devFunctionAppName)
                  package: '$(Pipeline.Workspace)/functionapp-artifact/CPS.Supplier.Functions.$(Build.BuildId).zip'

              - task: DownloadBuildArtifacts@0
                displayName: 'Download Web App Artifact'
                inputs:
                  buildType: current
                  downloadType: single
                  artifactName: webapp
                  downloadPath: '$(Pipeline.Workspace)'

              - task: AzureWebApp@1
                displayName: 'Deploy Web App to Azure'
                inputs:
                  azureSubscription: 'AzureDevops-Pipelines'
                  appName: $(devWebAppName)
                  package: '$(Pipeline.Workspace)/webapp/**/*.zip'

- stage: DeployQA
  displayName: 'Deploy to QA'
  dependsOn: BuildFunctionAndWeb
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/qa'))
  jobs:
    - deployment: DeployFunctionAndWebQA
      displayName: 'Deploy Function and Web App to QA'
      environment: qa
      strategy:
        runOnce:
          deploy:
            steps:
              - task: DownloadBuildArtifacts@0
                displayName: 'Download Function ZIP Artifact'
                inputs:
                  buildType: current
                  artifactName: functionapp-artifact
                  downloadPath: '$(Pipeline.Workspace)'

              - task: AzureFunctionApp@1
                displayName: 'Deploy Function App to Azure (QA)'
                inputs:
                  azureSubscription: 'AzureDevops-Pipelines'
                  appName: $(qaFunctionAppName)
                  package: '$(Pipeline.Workspace)/functionapp-artifact/CPS.Supplier.Functions.$(Build.BuildId).zip'

              - task: DownloadBuildArtifacts@0
                displayName: 'Download Web App Artifact'
                inputs:
                  buildType: current
                  downloadType: single
                  artifactName: webapp
                  downloadPath: '$(Pipeline.Workspace)'

              - task: AzureWebApp@1
                displayName: 'Deploy Web App to Azure (QA)'
                inputs:
                  azureSubscription: 'AzureDevops-Pipelines'
                  appName: $(qaWebAppName)
                  package: '$(Pipeline.Workspace)/webapp/**/*.zip'

- stage: DeploySQA
  displayName: 'Deploy to SQA'
  dependsOn: BuildFunctionAndWeb
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/sqa'))
  jobs:
    - deployment: DeployFunctionAndWebSQA
      displayName: 'Deploy Function and Web App to SQA'
      environment: sqa
      strategy:
        runOnce:
          deploy:
            steps:
              - task: DownloadBuildArtifacts@0
                displayName: 'Download Function ZIP Artifact'
                inputs:
                  buildType: current
                  artifactName: functionapp-artifact
                  downloadPath: '$(Pipeline.Workspace)'

              - task: AzureFunctionApp@1
                displayName: 'Deploy Function App to Azure (SQA)'
                inputs:
                  azureSubscription: 'AzureDevops-Pipelines'
                  appName: $(sqaFunctionAppName)
                  package: '$(Pipeline.Workspace)/functionapp-artifact/CPS.Supplier.Functions.$(Build.BuildId).zip'

              - task: DownloadBuildArtifacts@0
                displayName: 'Download Web App Artifact'
                inputs:
                  buildType: current
                  downloadType: single
                  artifactName: webapp
                  downloadPath: '$(Pipeline.Workspace)'

              - task: AzureWebApp@1
                displayName: 'Deploy Web App to Azure (SQA)'
                inputs:
                  azureSubscription: 'AzureDevops-Pipelines'
                  appName: $(sqaWebAppName)
                  package: '$(Pipeline.Workspace)/webapp/**/*.zip'