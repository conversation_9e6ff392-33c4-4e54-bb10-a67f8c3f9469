namespace CPS.Supplier.Application.Common;

/// <summary>
/// Operation types for authorization validation.
/// Extracted from authorization logic for Microsoft .NET 8 standards compliance.
/// </summary>
public static class OperationType
{
    public const string Map = "Map";
    public const string Unmap = "Unmap";
    public const string MarkValid = "MarkValid";
    public const string MarkInvalid = "MarkInvalid";
    public const string Approve = "Approve";
    public const string Reject = "Reject";
    public const string Complete = "Complete";
    public const string Delete = "Delete";
    public const string Upload = "Upload";
}