﻿using CPS.Supplier.Api.ClaimsTransformation;
using CPS.Supplier.Api.Localization;
using CPS.Supplier.Api.Middleware;
using CPS.Supplier.Api.PolicyProvider;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Models.AdministrationApi;
using CPS.Supplier.Application.Services.Auth0;
using CPS.Supplier.Application.Services.Auth0.Contracts;
using CPS.Supplier.Application.Services.Common;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Infrastructure;
using CPS.Supplier.Infrastructure.Health;
using CPS.Supplier.Persistence;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.IdentityModel.Tokens;
using Polly;
using Polly.Extensions.Http;
using Polly.Retry;
using Serilog;
using Swashbuckle.AspNetCore.SwaggerUI;

namespace CPS.Supplier.Api.Extensions
{
    internal static class StartupExtensions
    {

        private static readonly string[] SupportedCultures = { "en-US", "fr-CA" };
        public static WebApplication ConfigureServices(this WebApplicationBuilder builder)
        {
            AddSwagger(builder.Services);
            builder.Services.AddKeyVaultServices(builder.Configuration, builder.Environment);
            builder.Services.AddMemoryCache();
            builder.Services.AddHttpContextAccessor();

            ConfigureOptions(builder);
            ConfigureLocalization(builder);
            ConfigureAuthentication(builder);
            ConfigureAuthorization(builder);
            ConfigureApplicationInsights(builder);
            RegisterServiceLayers(builder);
            AddCorsService(builder);
            ConfigureHttpClients(builder);

            builder.Services.AddScoped<IClaimsTransformation, UserContextClaimsTransformation>();

            AddHealthChecks(builder);

            return builder.Build();
        }
        private static void AddHealthChecks(WebApplicationBuilder builder)
        {

            builder.Services.AddHealthChecks()
                .AddSqlServer(
                    builder.Configuration.GetConnectionString("SupplierConnectionString")
                        ?? throw new InvalidOperationException("Connection string 'CpsConnectionString' is not configured."),
                    name: "Database",
                    failureStatus: HealthStatus.Unhealthy)
                .AddCheck<SftpHealthCheck>("SFTP", HealthStatus.Unhealthy);
        }

        public static WebApplication ConfigurePipeline(this WebApplication app)
        {
            app.ConfigureGlobalExceptionHandler();

            app.UseMiddleware<RequestPerformanceMiddleware>();

            app.UseSerilogRequestLogging(options =>
            {
                options.MessageTemplate = "HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";
            });

            if (app.Environment.IsDevelopment()
               || app.Environment.EnvironmentName.Equals("Integration", StringComparison.OrdinalIgnoreCase))
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "CPS Supplier API v1");
                    c.DocExpansion(DocExpansion.None);
                    c.DefaultModelsExpandDepth(-1); // Hide schemas section
                    c.DisplayRequestDuration();
                });
            }

            app.UseHttpsRedirection();
            app.UseCors("Open");
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseRequestLocalization();

            app.MapEndpoints();

            app.MapGet("/ping", () => "OK");

            app.MapHealthChecks("/healthz", new HealthCheckOptions
            {
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            }).RequireAuthorization();

            return app;
        }

        private static void AddSwagger(IServiceCollection services)
        {
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new() { Title = "CPS Supplier API", Version = "v1" });

                // Add JWT Bearer authentication to Swagger
                c.AddSecurityDefinition("Bearer", new()
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = Microsoft.OpenApi.Models.ParameterLocation.Header,
                    Type = Microsoft.OpenApi.Models.SecuritySchemeType.Http,
                    Scheme = "bearer",
                    BearerFormat = "JWT"
                });
                c.AddSecurityRequirement(new()
                {
                    {
                        new()
                        {
                            Reference = new()
                            {
                                Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                });
            });
        }

        private static AsyncRetryPolicy<HttpResponseMessage> GetRetryPolicy(ConfigurationManager configuration)
        {
            var apsSettings = configuration.GetSection(AdministrationApiSettings.SectionName).Get<AdministrationApiSettings>() ?? new AdministrationApiSettings();

            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .WaitAndRetryAsync(
                    retryCount: apsSettings.Polly.RetryCount,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(apsSettings.Polly.BaseDelaySeconds * Math.Pow(2, retryAttempt - 1)),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        Log.Logger.Information("Retry {RetryCount} after {Timespan} seconds", retryCount, timespan);
                    });
        }

        private static void AddCorsService(WebApplicationBuilder builder)
        {
            builder.Services.AddCors(options =>
            {
                var allowedOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Value?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();

                options.AddPolicy("Open", builder => builder
                    .WithOrigins(allowedOrigins)
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials());
            });
        }

        private static void ConfigureOptions(WebApplicationBuilder builder)
        {
            // Note: Configuration is now handled in ApplicationServiceRegistration
            // This ensures consistent validation and registration patterns

            // Configure form options for file uploads
            builder.Services.Configure<FormOptions>(options =>
            {
                // Set the limit to 5 MB (matching FileRules.MaxFileUploadSizeMB)
                options.MultipartBodyLengthLimit = FileRules.MaxFileUploadSizeMb * 1024 * 1024; // 5 MB
                options.ValueLengthLimit = int.MaxValue;
                options.ValueCountLimit = int.MaxValue;
                options.KeyLengthLimit = int.MaxValue;
                options.MemoryBufferThreshold = 65536; // 64 KB
            });
        }

        private static void ConfigureAuthentication(WebApplicationBuilder builder)
        {
            builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            }).AddJwtBearer(options =>
            {
                options.Authority = builder.Configuration["Auth0:Domain"];
                options.Audience = builder.Configuration["Auth0:Audience"];
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                };
            });
        }

        private static void ConfigureAuthorization(WebApplicationBuilder builder)
        {
            builder.Services.AddAuthorization(options =>
            {
                //Added to aviod errors when using Authorize attribute
            });
            builder.Services.AddSingleton<IAuthorizationPolicyProvider, DynamicAuthorizationPolicyProvider>();
            builder.Services.AddScoped<IAuthorizationHandler, ModulePermissionHandler>();
            builder.Services.AddScoped<IAuthorizationHandler, OrModulePermissionHandler>();
        }

        private static void ConfigureApplicationInsights(WebApplicationBuilder builder)
        {
            builder.Services.AddApplicationInsightsTelemetry(options =>
            {
                options.ConnectionString = builder.Configuration["ApplicationInsights:ConnectionString"];
            });
        }

        private static void RegisterServiceLayers(WebApplicationBuilder builder)
        {
            builder.Services.AddApplicationServices(builder.Configuration);
            builder.Services.AddInfrastructureServices(builder.Configuration);
            builder.Services.AddPersistenceServices(builder.Configuration);
        }

        private static void ConfigureHttpClients(WebApplicationBuilder builder)
        {
            builder.Services.AddHttpClient<IAuth0TokenService, Auth0TokenService>()
                .AddPolicyHandler(GetRetryPolicy(builder.Configuration));
            builder.Services.AddHttpClient<IHttpClientService, HttpClientService>()
                .AddPolicyHandler(GetRetryPolicy(builder.Configuration));
        }

        private static void ConfigureLocalization(WebApplicationBuilder builder)
        {
            builder.Services.AddLocalization();
            builder.Services.Configure<RequestLocalizationOptions>(options =>
            {
                options.SetDefaultCulture("en-US")
                       .AddSupportedCultures(SupportedCultures)
                       .AddSupportedUICultures(SupportedCultures);

                options.RequestCultureProviders.Clear();
                options.RequestCultureProviders.Add(new UserContextCultureProvider());
            });
        }
    }
}