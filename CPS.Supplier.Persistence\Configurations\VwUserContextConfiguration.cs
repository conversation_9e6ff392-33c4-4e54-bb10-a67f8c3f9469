using CPS.Supplier.Domain.Entities.Models;

namespace CPS.Supplier.Persistence.Configurations;

public class VwUserContextConfiguration : IEntityTypeConfiguration<VwUserContext>
{
    public void Configure(EntityTypeBuilder<VwUserContext> builder)
    {
        builder
            .HasNoKey()
            .ToView("vw_UserContext", "SPL");

        builder.Property(e => e.UserId).HasColumnName("UserId");
        builder.Property(e => e.Username)
            .HasMaxLength(255)
            .IsUnicode(false)
            .HasColumnName("Username");
        builder.Property(e => e.RoleId).HasColumnName("RoleId");
        builder.Property(e => e.RoleName)
            .HasMaxLength(255)
            .IsUnicode(false)
            .HasColumnName("RoleName");
        builder.Property(e => e.PermissionId).HasColumnName("PermissionId");
        builder.Property(e => e.PermissionName)
            .HasMaxLength(255)
            .IsUnicode(false)
            .HasColumnName("PermissionName");
        builder.Property(e => e.ModuleId).HasColumnName("ModuleId");
        builder.Property(e => e.ModuleName)
            .HasMaxLength(255)
            .IsUnicode(false)
            .HasColumnName("ModuleName");
        builder.Property(e => e.SupplierId).HasColumnName("SupplierId");
        builder.Property(e => e.FacilityId).HasColumnName("FacilityId");
    }
}