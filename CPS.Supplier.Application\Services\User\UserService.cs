﻿using System.Globalization;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Exceptions;
using CPS.Supplier.Application.Models.AdministrationApi;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Application.Services.Localization;
using CPS.Supplier.Application.Services.User.Commands;
using CPS.Supplier.Application.Services.User.Contracts;
using CPS.Supplier.Application.Services.User.Dto;
using CPS.Supplier.Application.Services.UserContext.Contracts;
using CPS.Supplier.Application.Services.UserContext.Dto;
using CPS.Supplier.Domain.Entities;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CPS.Supplier.Application.Services.User;

public class UserService(
    IHttpClientService httpClientService,
    IOptions<AdministrationApiSettings> settings,
    ICurrentUserService currentUserService,
    ILocalizationService localizationService,
    ILogger<UserService> logger) : IUserService
{
    private readonly AdministrationApiSettings _apiSettings = settings.Value;

    public async Task<UserDetailDto?> GetUserByIdAsync(int userId, CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.User.GetUserDetailId.Replace("{userId}", userId.ToString(CultureInfo.InvariantCulture), StringComparison.OrdinalIgnoreCase);

        var result = await httpClientService.GetAsync<UserDetailDto>(endpoint, cancellationToken);

        return result;
    }

    public async Task<int> CreateUser(UpsertUserCommand command, CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.User.CreateSupplierUser;

        logger.LogDebug("Creating new user");

        var userId = await httpClientService.PostAsync<UpsertUserCommand, int>(endpoint, command, cancellationToken);

        if (userId > 0)
        {
            logger.LogDebug("Successfully created user with ID {UserId}", userId);

            return userId;
        }

        throw new BadRequestException(localizationService.GetString(ErrorMessages.UserCreationFailed));
    }

    public async Task<bool> UpdateUser(int userId, UpsertUserCommand command, CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Updating user with ID: {UserId}", userId);

        var endpoint = _apiSettings.User.UpdateUser.Replace("{userId}", userId.ToString(CultureInfo.InvariantCulture), StringComparison.OrdinalIgnoreCase);

        await httpClientService.PutAsync<UpsertUserCommand, object>(endpoint, command, cancellationToken);

        logger.LogDebug("Successfully updated user with ID: {UserId}", userId);

        return true;
    }

    public async Task UpdateUserStatusAsync(int userId, bool isActive, CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Updating user status for user with ID: {UserId}", userId);

        var endpoint = _apiSettings.User.UpdateSupplierUserStatus
            .Replace("{userId}", userId.ToString(CultureInfo.InvariantCulture), StringComparison.OrdinalIgnoreCase)
            + $"?isActive={isActive.ToString().ToUpper(CultureInfo.InvariantCulture)}";
        var command = new UpdateUserStatusCommand
        {
            IsActive = isActive
        };

        await httpClientService.PutAsync<UpdateUserStatusCommand, bool>(endpoint, command, cancellationToken);

        logger.LogDebug("Successfully updated user status for user with ID: {UserId}", userId);
    }

    public UserContextDto GetCurrentUserContext()
    {
        // Check for required supplier permissions
        if (!currentUserService.IsSuperAdmin && !currentUserService.HasAnyPermissionInModule(Modules.Supplier))
        {
            throw new ForbiddenException(localizationService.GetString(ErrorMessages.AccessForbidden));
        }

        var userId = currentUserService.UserId;

        // Create UserContextDto from current user's claims
        var userContext = new UserContextDto
        {
            UserId = userId,
            Username = currentUserService.Username ?? string.Empty,
            SupplierId = currentUserService.SupplierId,
            SupplierName = currentUserService.SupplierName,
            FacilityId = currentUserService.FacilityId,
            LanguageId = currentUserService.LanguageId,
            IsSuperAdmin = currentUserService.IsSuperAdmin,
        };

        // Populate roles from current user's claims
        foreach (var role in currentUserService.Roles)
        {
            userContext.Roles.Add(role);
        }

        // Populate module permissions from current user's claims
        var modulePermissions = currentUserService.ModulePermissions;
        foreach (var modulePermission in modulePermissions)
        {
            userContext.ModulePermissions[modulePermission.Key] = modulePermission.Value;
        }

        // Add custom claims to additional claims dictionary
        var customClaims = currentUserService.GetCustomClaims();
        foreach (var customClaim in customClaims)
        {
            userContext.AdditionalClaims[customClaim.Key] = customClaim.Value;
        }

        return userContext;
    }

    public async Task<PasswordPolicyDto> GetPasswordPolicy(CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.User.GetPasswordPolicy;
        bool allowAnonymous = true;
        var result = await httpClientService.GetAsync<PasswordPolicyDto>(endpoint, cancellationToken, allowAnonymous);
        return result ?? new PasswordPolicyDto();
    }

    public async Task<bool> EmailExists(string email, CancellationToken cancellationToken = default)
    {
        var endpoint = $"{_apiSettings.User.EmailExists}?email={Uri.EscapeDataString(email?.Trim() ?? string.Empty)}";
        var result = await httpClientService.GetAsync<bool>(endpoint, cancellationToken);
        return result;
    }

    public async Task<bool> UsernameExists(string username, CancellationToken cancellationToken = default)
    {
        var endpoint = $"{_apiSettings.User.UsernameExists}?username={Uri.EscapeDataString(username?.Trim() ?? string.Empty)}";
        var result = await httpClientService.GetAsync<bool>(endpoint, cancellationToken);
        return result;
    }
}