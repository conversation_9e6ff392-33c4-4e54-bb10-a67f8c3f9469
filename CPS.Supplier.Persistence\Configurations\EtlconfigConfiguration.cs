﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class EtlconfigConfiguration : IEntityTypeConfiguration<Etlconfig>
    {
        public void Configure(EntityTypeBuilder<Etlconfig> entity)
        {
            entity.HasKey(e => e.IntEtlconfigId)
                .HasName("PK_SSISConfiguration")
                .IsClustered(false);

            entity.ToTable("ETLConfig", "SPL");

            entity.Property(e => e.IntEtlconfigId).HasColumnName("intETLConfigID");
            entity.Property(e => e.StrKey)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("strKey");
            entity.Property(e => e.StrValue)
                .IsRequired()
                .HasMaxLength(4000)
                .IsUnicode(false)
                .HasColumnName("strValue");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<Etlconfig> entity);
    }
}
