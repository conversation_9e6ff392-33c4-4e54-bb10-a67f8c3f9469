﻿using CPS.Supplier.Application.Models.AdministrationApi;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Application.Services.MasterData.Contracts;
using CPS.Supplier.Application.Services.MasterData.Dto;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CPS.Supplier.Application.Services.MasterData;

public class MasterDataService : IMasterDataService
{
    private readonly IHttpClientService _httpClientService;
    private readonly AdministrationApiSettings _apiSettings;
    private readonly ILogger<MasterDataService> _logger;

    public MasterDataService(
        IHttpClientService httpClientService,
        IOptions<AdministrationApiSettings> settings,
        ILogger<MasterDataService> logger)
    {
        _httpClientService = httpClientService;
        _apiSettings = settings.Value;
        _logger = logger;
    }

    public async Task<IEnumerable<LanguageDto>?> GetCompanyLanguages(int companyId, CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.MasterData.Language;
        var fullEndpoint = $"{endpoint}?companyId={companyId}";

        _logger.LogDebug("Getting company languages. Endpoint: {Endpoint}, CompanyId: {CompanyId}",
            fullEndpoint, companyId);

        var result = await _httpClientService.GetAsync<IEnumerable<LanguageDto>>(fullEndpoint, cancellationToken);

        if (result != null)
        {
            _logger.LogInformation("Successfully retrieved {Count} company languages for company {CompanyId}", result.Count(), companyId);
        }

        return result;
    }

    public async Task<IEnumerable<StatusDto>?> GetStatuses(CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.MasterData.Status;

        _logger.LogDebug("Getting statuses from Administration API");

        var result = await _httpClientService.GetAsync<IEnumerable<StatusDto>>(endpoint, cancellationToken);

        if (result != null)
        {
            _logger.LogInformation("Successfully retrieved {Count} statuses", result.Count());
        }

        return result;
    }

    public async Task<IEnumerable<AddressTypeDto>?> GetAddressTypes(CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.MasterData.AddressType;

        _logger.LogDebug("Getting address types from Administration API");

        var result = await _httpClientService.GetAsync<IEnumerable<AddressTypeDto>>(endpoint, cancellationToken);

        if (result != null)
        {
            _logger.LogInformation("Successfully retrieved {Count} address types", result.Count());
        }

        return result;
    }

    public async Task<IEnumerable<PhoneTypeDto>?> GetPhoneTypes(CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.MasterData.PhoneType;

        _logger.LogDebug("Getting phone types from Administration API");

        var result = await _httpClientService.GetAsync<IEnumerable<PhoneTypeDto>>(endpoint, cancellationToken);

        if (result != null)
        {
            _logger.LogInformation("Successfully retrieved {Count} phone types", result.Count());
        }

        return result;
    }

    public async Task<IEnumerable<CountryDto>?> GetCountries(CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.MasterData.Countries;

        _logger.LogDebug("Getting countries from Administration API");

        var result = await _httpClientService.GetAsync<IEnumerable<CountryDto>>(endpoint, cancellationToken);

        if (result != null)
        {
            _logger.LogDebug("Successfully retrieved {Count} countries", result.Count());
        }

        return result;
    }

    public async Task<IEnumerable<ProvinceDto>?> GetProvinces(int? countryId, CancellationToken cancellationToken)
    {
        var endpoint = _apiSettings.MasterData.Provinces;
        var fullEndpoint = $"{endpoint}?countryId={countryId}";

        _logger.LogDebug("Getting provinces from Administration API");

        var result = await _httpClientService.GetAsync<IEnumerable<ProvinceDto>>(fullEndpoint, cancellationToken);

        if (result != null)
        {
            _logger.LogDebug("Successfully retrieved {Count} provinces", result.Count());
        }

        return result;
    }

    public async Task<IEnumerable<RoleDto>?> GetAllRoles(CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.MasterData.Role;

        _logger.LogDebug("Getting roles from Administration API");

        var result = await _httpClientService.GetAsync<IEnumerable<RoleDto>>(endpoint, cancellationToken);

        if (result != null)
        {
            _logger.LogDebug("Successfully retrieved {Count} roles", result.Count());
        }

        return result;
    }

    public async Task<IEnumerable<CountryCodeDto>?> GetCountryCodes(CancellationToken cancellationToken = default)
    {
        var endpoint = _apiSettings.MasterData.CountryCodes;

        _logger.LogDebug("Getting country codes from Administration API");

        var result = await _httpClientService.GetAsync<IEnumerable<CountryCodeDto>>(endpoint, cancellationToken);

        if (result != null)
        {
            _logger.LogDebug("Successfully retrieved {Count} country codes", result.Count());
        }

        return result;
    }
}