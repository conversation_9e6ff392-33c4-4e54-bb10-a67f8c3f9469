﻿using CPS.Supplier.Api.Infrastructure;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.MasterData.Contracts;
using CPS.Supplier.Application.Services.MasterData.Dto;
using Microsoft.AspNetCore.Http.HttpResults;

namespace CPS.Supplier.Api.Endpoints;

public class MasterData : IEndpointGroupBase
{
    public void Map(WebApplication app)
    {
        var group = app.MapGroup(this);
        group.RequireAuthorization();
        group.MapGet(GetCompanyLanguages, "/company-languages");
        group.MapGet(GetStatuses, "/statuses");
        group.MapGet(GetAddressTypes, "/address-types");
        group.MapGet(GetPhoneTypes, "/phone-types");
        group.MapGet(GetCountries, "/countries");
        group.MapGet(GetProvinces, "/provinces");
        group.MapGet(GetAllRoles, "/roles");
        group.MapGet(GetCountryCodes, "/country-codes");
    }

    private static async Task<IResult> GetCompanyLanguages(
        IMasterDataService masterService,
        int companyId,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("MasterDataApi.company-languages.Count", 1);
        var results = await masterService.GetCompanyLanguages(companyId, ct);
        return results != null ? TypedResults.Ok(results) : TypedResults.NotFound();
    }

    private static async Task<IResult> GetStatuses(
        IMasterDataService masterService,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("MasterDataApi.statuses.Count", 1);
        var results = await masterService.GetStatuses(ct);
        return results != null ? TypedResults.Ok(results) : TypedResults.NotFound();
    }

    private static async Task<IResult> GetAddressTypes(
        IMasterDataService masterService,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("MasterDataApi.address-types.Count", 1);
        var results = await masterService.GetAddressTypes(ct);
        return results != null ? TypedResults.Ok(results) : TypedResults.NotFound();
    }

    private static async Task<IResult> GetPhoneTypes(
        IMasterDataService masterService,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("MasterDataApi.phone-types.Count", 1);
        var results = await masterService.GetPhoneTypes(ct);
        return results != null ? TypedResults.Ok(results) : TypedResults.NotFound();
    }

    private static async Task<Ok<IEnumerable<CountryDto>>> GetCountries(
        IMasterDataService masterService,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("MasterDataApi.countries.Count", 1);
        var result = await masterService.GetCountries(ct);
        return TypedResults.Ok(result);
    }

    private static async Task<Ok<IEnumerable<ProvinceDto>>> GetProvinces(
        int? countryId,
        IMasterDataService masterService,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("MasterDataApi.provinces.Count", 1);
        var result = await masterService.GetProvinces(countryId, ct);
        return TypedResults.Ok(result);
    }

    private static async Task<Ok<IEnumerable<RoleDto>>> GetAllRoles(
        IMasterDataService masterService,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("MasterDataApi.roles.Count", 1);
        var result = await masterService.GetAllRoles(ct);
        return TypedResults.Ok(result);
    }

    private static async Task<Ok<IEnumerable<CountryCodeDto>>> GetCountryCodes(
        IMasterDataService masterService,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("MasterDataApi.country-codes.Count", 1);
        var result = await masterService.GetCountryCodes(ct);
        return TypedResults.Ok(result);
    }
}