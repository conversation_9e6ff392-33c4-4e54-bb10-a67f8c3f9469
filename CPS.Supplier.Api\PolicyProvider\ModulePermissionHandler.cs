﻿using CPS.Supplier.Application.Services.UserContext.Contracts;
using Microsoft.AspNetCore.Authorization;

namespace CPS.Supplier.Api.PolicyProvider;

public class ModulePermissionHandler(ICurrentUserService currentUserService) : AuthorizationHandler<ModulePermissionRequirement>
{
    protected override Task HandleRequirementAsync(
       AuthorizationHandlerContext context,
       ModulePermissionRequirement requirement)
    {
        if (currentUserService.IsSuperAdmin)
        {
            context.Succeed(requirement);
            return Task.CompletedTask;
        }

        // Regular permission check for non-SuperAdmin users
        if (currentUserService.HasPermissionInModule(requirement.Module, requirement.Permission))
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}