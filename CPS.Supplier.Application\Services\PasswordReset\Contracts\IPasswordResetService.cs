﻿using CPS.Supplier.Application.Services.PasswordReset.Commands;
using CPS.Supplier.Application.Services.PasswordReset.Dto;

namespace CPS.Supplier.Application.Services.PasswordReset.Contracts;

public interface IPasswordResetService
{
    Task<PasswordResetRequestResponseDto> RequestPasswordResetAsync(PasswordResetRequestCommand command, CancellationToken cancellationToken);
    Task<PasswordResetResponseDto> ResetPasswordAsync(PasswordResetCommand command, CancellationToken cancellationToken);
    Task<ValidatePasswordResetTokenResponseDto> ValidatePasswordResetTokenAsync(ValidatePasswordResetTokenCommand command, CancellationToken cancellationToken);
}