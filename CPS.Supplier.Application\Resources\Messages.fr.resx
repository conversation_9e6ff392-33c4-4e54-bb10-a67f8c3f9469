<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Status Labels (for UI-only statuses) -->
  <data name="StatusLabel_Processing" xml:space="preserve">
    <value>En traitement</value>
  </data>

  <!-- Status Messages -->
  <data name="StatusMessages_Uploaded" xml:space="preserve">
    <value>Le fichier a été téléchargé et est en file d'attente pour traitement</value>
  </data>
  <data name="StatusMessages_Processing" xml:space="preserve">
    <value>Le fichier est en cours de traitement</value>
  </data>
  <data name="StatusMessages_ExternalMapping" xml:space="preserve">
    <value>Le fichier nécessite une révision manuelle du mappage</value>
  </data>
  <data name="StatusMessages_Approved" xml:space="preserve">
    <value>Le fichier a été approuvé et traité</value>
  </data>
  <data name="StatusMessages_Rejected" xml:space="preserve">
    <value>Le fichier a été rejeté</value>
  </data>
  <data name="StatusMessages_Completed" xml:space="preserve">
    <value>Le traitement du fichier est terminé</value>
  </data>
  <data name="StatusMessages_Unknown" xml:space="preserve">
    <value>Statut inconnu</value>
  </data>
  <data name="StatusMessages_NotFound" xml:space="preserve">
    <value>Fichier non trouvé</value>
  </data>


  
  <!-- Validation Messages -->
  <data name="ValidationMessages_FileRequired" xml:space="preserve">
    <value>Le fichier est requis</value>
  </data>
  <data name="ValidationMessages_InvalidCsvFormat" xml:space="preserve">
    <value>Format CSV invalide. Le fichier doit être séparé par des virgules ou des points-virgules.</value>
  </data>
  <data name="ValidationMessages_ValidSupplierIdRequired" xml:space="preserve">
    <value>Un ID de fournisseur valide est requis pour les utilisateurs externes</value>
  </data>
  <data name="ValidationMessages_SupplierNotFound" xml:space="preserve">
    <value>Fournisseur non trouvé</value>
  </data>
  <data name="ValidationMessages_SupplierNotActive" xml:space="preserve">
    <value>Le fournisseur n'est pas actif</value>
  </data>
  <data name="ValidationMessages_SameMonthUploadExists" xml:space="preserve">
    <value>Un seul fichier peut être téléchargé par mois. Veuillez réessayer le mois prochain.</value>
  </data>

  <!-- File Validation Messages (moved from ResponseMessages) -->
  <data name="ValidationMessages_InvalidFileSize" xml:space="preserve">
    <value>La taille du fichier dépasse la limite maximale autorisée</value>
  </data>
  <data name="ValidationMessages_InvalidFileExtension" xml:space="preserve">
    <value>Extension de fichier invalide. Seuls les fichiers CSV sont autorisés</value>
  </data>
  <data name="ValidationMessages_FileNameTooLong" xml:space="preserve">
    <value>Le nom du fichier doit contenir moins de 100 caractères</value>
  </data>

  <!-- NEW: VolumeFileProcess Validation Messages (French) -->
  <data name="ValidationMessages_FileNameRequired" xml:space="preserve">
    <value>Le nom de fichier est requis</value>
  </data>
  <data name="ValidationMessages_BlobPathRequired" xml:space="preserve">
    <value>Le chemin du blob est requis</value>
  </data>
  <data name="ValidationMessages_ContainerNameRequired" xml:space="preserve">
    <value>Le nom du conteneur est requis</value>
  </data>
  <data name="ValidationMessages_ProcessingMonthRequiredWithFormat" xml:space="preserve">
    <value>Le mois de traitement est requis (format: AAAAMMM)</value>
  </data>
  <data name="ValidationMessages_UserSKRequired" xml:space="preserve">
    <value>UserSK est requis</value>
  </data>
  <data name="ValidationMessages_SupplierIdRequired" xml:space="preserve">
    <value>L'ID du fournisseur est requis</value>
  </data>
  <data name="ValidationMessages_ProcessingMonthInvalidFormat" xml:space="preserve">
    <value>Le mois de traitement doit être au format AAAAMMM</value>
  </data>

  <!-- NEW: HttpClient Error Messages (French) -->
  <data name="ErrorMessages_RequestFailed" xml:space="preserve">
    <value>La demande a échoué</value>
  </data>
  <data name="ErrorMessages_ResourceNotFound" xml:space="preserve">
    <value>Ressource non trouvée</value>
  </data>
  <data name="ErrorMessages_AccessForbidden" xml:space="preserve">
    <value>Accès interdit</value>
  </data>
  <data name="ErrorMessages_Unauthorized" xml:space="preserve">
    <value>Non autorisé</value>
  </data>

  <!-- NEW: Global Exception Messages (French) -->
  <data name="ErrorMessages_ValidationFailed" xml:space="preserve">
    <value>La validation a échoué</value>
  </data>

  <!-- NEW: Technical Error Messages (French) -->
  <data name="ErrorMessages_TechnicalError" xml:space="preserve">
    <value>Erreur technique - Un problème inattendu s'est produit. Veuillez réessayer plus tard.
Si le problème persiste, contactez le support client.</value>
  </data>

  <!-- NEW: Unexpected Error Messages (French) -->
  <data name="ErrorMessages_UnexpectedError" xml:space="preserve">
    <value>Un problème inattendu s'est produit. Veuillez réessayer plus tard. Si le problème persiste, contactez le support client.</value>
  </data>

  <!-- File Operation Error Messages (French) -->
  <data name="ErrorMessages_InvalidFileStatus" xml:space="preserve">
    <value>Le statut du fichier doit être Audit Aramark</value>
  </data>
  <data name="ErrorMessages_UserCreationFailed" xml:space="preserve">
    <value>Échec de la création de l'utilisateur</value>
  </data>
  <data name="ErrorMessages_UnknownField" xml:space="preserve">
    <value>Champ inconnu</value>
  </data>
  <data name="ErrorMessages_InsufficientPermissions" xml:space="preserve">
    <value>Permissions insuffisantes pour cette opération</value>
  </data>
  <data name="ErrorMessages_SupplierAccessOnly" xml:space="preserve">
    <value>Vous ne pouvez accéder qu'aux fichiers appartenant à votre fournisseur</value>
  </data>

  <!-- File Operation Validation Messages (French) -->
  <data name="ValidationMessages_MappingRequired" xml:space="preserve">
    <value>Impossible d'approuver le fichier. Tous les enregistrements doivent être mappés ou marqués comme invalides avant l'approbation.</value>
  </data>
  <data name="ValidationMessages_InvalidRecordStatus" xml:space="preserve">
    <value>L'enregistrement ne peut pas être marqué comme invalide à partir de son statut actuel</value>
  </data>
  <data name="ValidationMessages_CustomerNotFound" xml:space="preserve">
    <value>L'ID client Aramark '{0}' n'existe pas</value>
  </data>
  <data name="ValidationMessages_OnlyInvalidRecordsCanBeMarkedValid" xml:space="preserve">
    <value>Seuls les enregistrements invalides peuvent être marqués comme valides</value>
  </data>

  <!-- Complete File Validation Message (French) -->
  <data name="ValidationMessages_CompleteFileRequiresMappedRecords" xml:space="preserve">
    <value>Impossible de marquer le fichier comme terminé. Tous les enregistrements doivent être mappés ou marqués comme invalides.</value>
  </data>

</root>
