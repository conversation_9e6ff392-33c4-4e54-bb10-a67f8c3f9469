﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <NeutralLanguage>en-US</NeutralLanguage>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" />
        <PackageReference Include="SSH.NET" />
        <PackageReference Include="Azure.Storage.Queues" />
        <PackageReference Include="Meziantou.Analyzer">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Abstractions" />
        <PackageReference Include="Microsoft.Data.SqlClient" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" />
        <PackageReference Include="FluentValidation" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
        <PackageReference Include="FuzzySharp" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
        <PackageReference Include="Microsoft.Extensions.Configuration" />
        <PackageReference Include="Microsoft.Extensions.Localization" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\CPS.Supplier.Domain\CPS.Supplier.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Interfaces\Services\" />
        <Folder Include="Services\PasswordReset\Contracts\Dto\" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Resources\Messages.Designer.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>Messages.resx</DependentUpon>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="Resources\Messages.fr.resx">
        <Generator>PublicResXFileCodeGenerator</Generator>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Messages.resx">
        <Generator>PublicResXFileCodeGenerator</Generator>
        <LastGenOutput>Messages.Designer.cs</LastGenOutput>
      </EmbeddedResource>
    </ItemGroup>

</Project>