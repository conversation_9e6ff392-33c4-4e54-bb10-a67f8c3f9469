using CPS.Supplier.Application.Exceptions;
using CPS.Supplier.Application.Models.AdministrationApi;
using Microsoft.Extensions.Options;

namespace CPS.Supplier.Application.Services.Common;

public class AdministrationApiSettingsValidator : IValidateOptions<AdministrationApiSettings>
{
    public ValidateOptionsResult Validate(string? name, AdministrationApiSettings options)
    {
        try
        {
            ValidateRequiredSettings(options);

            return ValidateOptionsResult.Success;
        }
        catch (NotFoundException ex)
        {
            return ValidateOptionsResult.Fail(ex.Message);
        }
    }

    private static void ValidateRequiredSettings(AdministrationApiSettings settings)
    {
        if (settings == null)
        {
            throw new NotFoundException("AdministrationApi configuration section is missing");
        }

        if (string.IsNullOrWhiteSpace(settings.BaseUrl))
        {
            throw new NotFoundException("AdministrationApi:BaseUrl configuration is required");
        }

        ValidateAuth0Settings(settings.Auth0);

        ValidateMasterDataSettings(settings.MasterData);

        ValidateUserSettings(settings.User);

        ValidatePollySettings(settings.Polly);
    }

    private static void ValidateAuth0Settings(Auth0Settings auth0)
    {
        if (string.IsNullOrWhiteSpace(auth0.TokenUrl))
        {
            throw new NotFoundException("AdministrationApi:Auth0:TokenUrl configuration is required");
        }

        if (string.IsNullOrWhiteSpace(auth0.ClientId))
        {
            throw new NotFoundException("AdministrationApi:Auth0:ClientId configuration is required");
        }

        if (string.IsNullOrWhiteSpace(auth0.ClientSecret))
        {
            throw new NotFoundException("AdministrationApi:Auth0:ClientSecret configuration is required");
        }

        if (string.IsNullOrWhiteSpace(auth0.M2MAudience))
        {
            throw new NotFoundException("AdministrationApi:Auth0:M2MAudience configuration is required");
        }
    }

    private static void ValidateMasterDataSettings(MasterDataSettings masterData)
    {
        if (string.IsNullOrWhiteSpace(masterData.Language))
        {
            throw new NotFoundException("AdministrationApi:MasterData:Language configuration is required");
        }

        if (string.IsNullOrWhiteSpace(masterData.Status))
        {
            throw new NotFoundException("AdministrationApi:MasterData:Status configuration is required");
        }

        if (string.IsNullOrWhiteSpace(masterData.AddressType))
        {
            throw new NotFoundException("AdministrationApi:MasterData:AddressType configuration is required");
        }

        if (string.IsNullOrWhiteSpace(masterData.PhoneType))
        {
            throw new NotFoundException("AdministrationApi:MasterData:PhoneType configuration is required");
        }
    }

    private static void ValidateUserSettings(UserSettings user)
    {
        if (string.IsNullOrWhiteSpace(user.GetUserDetailId))
        {
            throw new NotFoundException("AdministrationApi:User:GetUserDetailId configuration is required");
        }
    }

    private static void ValidatePollySettings(PollySettings polly)
    {
        if (polly.RetryCount < 0)
        {
            throw new NotFoundException("AdministrationApi:Polly:RetryCount must be a non-negative integer");
        }

        if (polly.BaseDelaySeconds <= 0)
        {
            throw new NotFoundException("AdministrationApi:Polly:BaseDelaySeconds must be a positive number");
        }

        if (polly.TimeoutSeconds <= 0)
        {
            throw new NotFoundException("AdministrationApi:Polly:TimeoutSeconds must be a positive integer");
        }
    }
}