﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CPS.Supplier.Application.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Messages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Messages() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("CPS.Supplier.Application.Resources.Messages", typeof(Messages).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access forbidden.
        /// </summary>
        public static string ErrorMessages_AccessForbidden {
            get {
                return ResourceManager.GetString("ErrorMessages_AccessForbidden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request failed.
        /// </summary>
        public static string ErrorMessages_RequestFailed {
            get {
                return ResourceManager.GetString("ErrorMessages_RequestFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource not found.
        /// </summary>
        public static string ErrorMessages_ResourceNotFound {
            get {
                return ResourceManager.GetString("ErrorMessages_ResourceNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Technical Error - An unexpected issue occurred. Please try again later.
        ///If the problem persists, contact Customer Support..
        /// </summary>
        public static string ErrorMessages_TechnicalError {
            get {
                return ResourceManager.GetString("ErrorMessages_TechnicalError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unauthorized.
        /// </summary>
        public static string ErrorMessages_Unauthorized {
            get {
                return ResourceManager.GetString("ErrorMessages_Unauthorized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validation failed.
        /// </summary>
        public static string ErrorMessages_ValidationFailed {
            get {
                return ResourceManager.GetString("ErrorMessages_ValidationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All records must be mapped before file can be approved.
        /// </summary>
        public static string ResponseMessages_AllRecordsMustBeMapped {
            get {
                return ResourceManager.GetString("ResponseMessages_AllRecordsMustBeMapped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Found.
        /// </summary>
        public static string ResponseMessages_CustomerNotFound {
            get {
                return ResourceManager.GetString("ResponseMessages_CustomerNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Mapped.
        /// </summary>
        public static string ResponseMessages_CustomerNotMapped {
            get {
                return ResourceManager.GetString("ResponseMessages_CustomerNotMapped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown.
        /// </summary>
        public static string ResponseMessages_CustomerUnknown {
            get {
                return ResourceManager.GetString("ResponseMessages_CustomerUnknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A file already exists for this month and is not in error state.
        /// </summary>
        public static string ResponseMessages_FileAlreadyExistsForMonth {
            get {
                return ResourceManager.GetString("ResponseMessages_FileAlreadyExistsForMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approval failed.
        /// </summary>
        public static string ResponseMessages_FileApprovalFailed {
            get {
                return ResourceManager.GetString("ResponseMessages_FileApprovalFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File approved successfully.
        /// </summary>
        public static string ResponseMessages_FileApprovalSuccess {
            get {
                return ResourceManager.GetString("ResponseMessages_FileApprovalSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deletion failed.
        /// </summary>
        public static string ResponseMessages_FileDeletionFailed {
            get {
                return ResourceManager.GetString("ResponseMessages_FileDeletionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File deleted successfully.
        /// </summary>
        public static string ResponseMessages_FileDeletionSuccess {
            get {
                return ResourceManager.GetString("ResponseMessages_FileDeletionSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File name should be less than 100 characters.
        /// </summary>
        public static string ResponseMessages_FileNameTooLong {
            get {
                return ResourceManager.GetString("ResponseMessages_FileNameTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejection failed.
        /// </summary>
        public static string ResponseMessages_FileRejectionFailed {
            get {
                return ResourceManager.GetString("ResponseMessages_FileRejectionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File rejected successfully.
        /// </summary>
        public static string ResponseMessages_FileRejectionSuccess {
            get {
                return ResourceManager.GetString("ResponseMessages_FileRejectionSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File replacement is only allowed when the previous file has errors.
        /// </summary>
        public static string ResponseMessages_FileReplacementNotAllowed {
            get {
                return ResourceManager.GetString("ResponseMessages_FileReplacementNotAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File uploaded successfully.
        /// </summary>
        public static string ResponseMessages_FileUploadSuccess {
            get {
                return ResourceManager.GetString("ResponseMessages_FileUploadSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File validation failed.
        /// </summary>
        public static string ResponseMessages_FileValidationFailed {
            get {
                return ResourceManager.GetString("ResponseMessages_FileValidationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file extension. Only CSV files are allowed.
        /// </summary>
        public static string ResponseMessages_InvalidFileExtension {
            get {
                return ResourceManager.GetString("ResponseMessages_InvalidFileExtension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file format or empty file.
        /// </summary>
        public static string ResponseMessages_InvalidFileFormat {
            get {
                return ResourceManager.GetString("ResponseMessages_InvalidFileFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File size exceeds maximum allowed limit.
        /// </summary>
        public static string ResponseMessages_InvalidFileSize {
            get {
                return ResourceManager.GetString("ResponseMessages_InvalidFileSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processing month must be in YYYYMM format.
        /// </summary>
        public static string ResponseMessages_InvalidProcessingMonthFormat {
            get {
                return ResourceManager.GetString("ResponseMessages_InvalidProcessingMonthFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update failed.
        /// </summary>
        public static string ResponseMessages_MappingUpdateFailed {
            get {
                return ResourceManager.GetString("ResponseMessages_MappingUpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mapping updated successfully.
        /// </summary>
        public static string ResponseMessages_MappingUpdateSuccess {
            get {
                return ResourceManager.GetString("ResponseMessages_MappingUpdateSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File contains data for multiple months.
        /// </summary>
        public static string ResponseMessages_MultipleMonthsDetected {
            get {
                return ResourceManager.GetString("ResponseMessages_MultipleMonthsDetected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File is currently being processed.
        /// </summary>
        public static string ResponseMessages_ProcessingInProgress {
            get {
                return ResourceManager.GetString("ResponseMessages_ProcessingInProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown error.
        /// </summary>
        public static string ResponseMessages_UnknownError {
            get {
                return ResourceManager.GetString("ResponseMessages_UnknownError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload is disabled while another file is being processed.
        /// </summary>
        public static string ResponseMessages_UploadDisabledDuringProcessing {
            get {
                return ResourceManager.GetString("ResponseMessages_UploadDisabledDuringProcessing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File has been approved and processed.
        /// </summary>
        public static string StatusMessages_Approved {
            get {
                return ResourceManager.GetString("StatusMessages_Approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File processing is complete.
        /// </summary>
        public static string StatusMessages_Completed {
            get {
                return ResourceManager.GetString("StatusMessages_Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File requires manual mapping review.
        /// </summary>
        public static string StatusMessages_ExternalMapping {
            get {
                return ResourceManager.GetString("StatusMessages_ExternalMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File not found.
        /// </summary>
        public static string StatusMessages_NotFound {
            get {
                return ResourceManager.GetString("StatusMessages_NotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File is currently being processed.
        /// </summary>
        public static string StatusMessages_Processing {
            get {
                return ResourceManager.GetString("StatusMessages_Processing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File has been rejected.
        /// </summary>
        public static string StatusMessages_Rejected {
            get {
                return ResourceManager.GetString("StatusMessages_Rejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status unknown.
        /// </summary>
        public static string StatusMessages_Unknown {
            get {
                return ResourceManager.GetString("StatusMessages_Unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File has been uploaded and is queued for processing.
        /// </summary>
        public static string StatusMessages_Uploaded {
            get {
                return ResourceManager.GetString("StatusMessages_Uploaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Blank row found at line {0}.
        /// </summary>
        public static string ValidationMessages_BlankRowFound {
            get {
                return ResourceManager.GetString("ValidationMessages_BlankRowFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BlobPath is required.
        /// </summary>
        public static string ValidationMessages_BlobPathRequired {
            get {
                return ResourceManager.GetString("ValidationMessages_BlobPathRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ContainerName is required.
        /// </summary>
        public static string ValidationMessages_ContainerNameRequired {
            get {
                return ResourceManager.GetString("ValidationMessages_ContainerNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSV content validation failed.
        /// </summary>
        public static string ValidationMessages_CsvContentValidationFailed {
            get {
                return ResourceManager.GetString("ValidationMessages_CsvContentValidationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File with same name already exists for this supplier.
        /// </summary>
        public static string ValidationMessages_FileNameAlreadyExists {
            get {
                return ResourceManager.GetString("ValidationMessages_FileNameAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FileName is required.
        /// </summary>
        public static string ValidationMessages_FileNameRequired {
            get {
                return ResourceManager.GetString("ValidationMessages_FileNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File is required.
        /// </summary>
        public static string ValidationMessages_FileRequired {
            get {
                return ResourceManager.GetString("ValidationMessages_FileRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid CSV format. File must be comma or semicolon separated..
        /// </summary>
        public static string ValidationMessages_InvalidCsvFormat {
            get {
                return ResourceManager.GetString("ValidationMessages_InvalidCsvFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ProcessingMonth must be in YYYYMM format.
        /// </summary>
        public static string ValidationMessages_ProcessingMonthInvalidFormat {
            get {
                return ResourceManager.GetString("ValidationMessages_ProcessingMonthInvalidFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processing month is required.
        /// </summary>
        public static string ValidationMessages_ProcessingMonthRequired {
            get {
                return ResourceManager.GetString("ValidationMessages_ProcessingMonthRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ProcessingMonth is required (format: YYYYMM).
        /// </summary>
        public static string ValidationMessages_ProcessingMonthRequiredWithFormat {
            get {
                return ResourceManager.GetString("ValidationMessages_ProcessingMonthRequiredWithFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required column {0} is empty at line {1}.
        /// </summary>
        public static string ValidationMessages_RequiredColumnEmpty {
            get {
                return ResourceManager.GetString("ValidationMessages_RequiredColumnEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File already uploaded for {0}.
        /// </summary>
        public static string ValidationMessages_SameMonthUploadExists {
            get {
                return ResourceManager.GetString("ValidationMessages_SameMonthUploadExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SupplierId is required.
        /// </summary>
        public static string ValidationMessages_SupplierIdRequired {
            get {
                return ResourceManager.GetString("ValidationMessages_SupplierIdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier is not active.
        /// </summary>
        public static string ValidationMessages_SupplierNotActive {
            get {
                return ResourceManager.GetString("ValidationMessages_SupplierNotActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier not found.
        /// </summary>
        public static string ValidationMessages_SupplierNotFound {
            get {
                return ResourceManager.GetString("ValidationMessages_SupplierNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User not found for this supplier.
        /// </summary>
        public static string ValidationMessages_UserNotFoundForSupplier {
            get {
                return ResourceManager.GetString("ValidationMessages_UserNotFoundForSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserSK is required.
        /// </summary>
        public static string ValidationMessages_UserSKRequired {
            get {
                return ResourceManager.GetString("ValidationMessages_UserSKRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Valid supplier ID is required for external users.
        /// </summary>
        public static string ValidationMessages_ValidSupplierIdRequired {
            get {
                return ResourceManager.GetString("ValidationMessages_ValidSupplierIdRequired", resourceCulture);
            }
        }
    }
}
