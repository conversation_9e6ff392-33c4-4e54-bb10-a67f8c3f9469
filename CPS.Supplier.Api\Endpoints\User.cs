﻿using CPS.Supplier.Api.Infrastructure;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.User.Commands;
using CPS.Supplier.Application.Services.User.Contracts;
using CPS.Supplier.Application.Services.User.Dto;
using CPS.Supplier.Application.Services.UserContext.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;

namespace CPS.Supplier.Api.Endpoints;

public class User : IEndpointGroupBase
{
    public void Map(WebApplication app)
    {
        var group = app.MapGroup(this);
        group.RequireAuthorization();
        group.MapGet(GetUserById, "/{userId:int}/detail");
        group.MapPost(CreateUser, "/create");
        group.MapPut(UpdateUser, "/{userId:int}/update");
        group.MapGet(GetCurrentUserContext, "/context");
        group.MapPut(UpdateUserStatus, "/{userId:int}/update-status");
        group.MapGet(EmailExists, "/email-exists");
        group.MapGet(UsernameExists, "/username-exists");
        group.MapGet(GetPasswordPolicy, "/password-policy");
    }

    [Authorize(Policy = Policies.ViewUserOrManage)]
    private static async Task<Results<Ok<UserDetailDto>, NotFound>> GetUserById(
        IUserService administrationUserService,
        int userId,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("UserApi.detail.Count", 1);
        var result = await administrationUserService.GetUserByIdAsync(userId, ct);
        return TypedResults.Ok(result);
    }

    [Authorize(Policy = Policies.CreateUser)]
    private static async Task<Results<Created<int>, BadRequest>> CreateUser(
        IUserService administrationUserService,
        UpsertUserCommand command,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("UserApi.create.Count", 1);
        var userId = await administrationUserService.CreateUser(command, ct);
        return TypedResults.Created($"/user/{userId}", userId);
    }

    [Authorize(Policy = Policies.EditUser)]
    private static async Task<Results<NoContent, BadRequest>> UpdateUser(int userId, UpsertUserCommand editUserDto, IUserService administrationUserService, IMetricsService metricsService, CancellationToken ct)
    {
        metricsService.TrackMetric("UserApi.update.Count", 1);
        var result = await administrationUserService.UpdateUser(userId, editUserDto, ct);
        return result ? TypedResults.NoContent() : TypedResults.BadRequest();
    }

    public static Ok<UserContextDto> GetCurrentUserContext(IUserService userService)
    => TypedResults.Ok(userService.GetCurrentUserContext());

    [Authorize(Policy = Policies.EditUser)]
    public static async Task<NoContent> UpdateUserStatus(
    int userId,
    bool isActive,
    IUserService administrationUserService,
    IMetricsService metricsService,
    CancellationToken ct)
    {
        metricsService.TrackMetric("UserApi.update-status.Count", 1);
        await administrationUserService.UpdateUserStatusAsync(userId, isActive, ct);
        return TypedResults.NoContent();
    }

    [Authorize(Policy = Policies.ViewUserOrManage)]
    public static async Task<Ok<bool>> EmailExists(
        IUserService userService,
        string email,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("UserApi.email-exists.Count", 1);
        var exists = await userService.EmailExists(email, ct);
        return TypedResults.Ok(exists);
    }

    [Authorize(Policy = Policies.ViewUserOrManage)]
    public static async Task<Ok<bool>> UsernameExists(
        IUserService userService,
        string username,
        IMetricsService metricsService,
        CancellationToken ct)
    {
        metricsService.TrackMetric("UserApi.username-exists.Count", 1);
        var exists = await userService.UsernameExists(username, ct);
        return TypedResults.Ok(exists);
    }

    [AllowAnonymous]
    public static async Task<Ok<PasswordPolicyDto>> GetPasswordPolicy(IUserService userService, IMetricsService metricsService, CancellationToken ct)
    {
        metricsService.TrackMetric("UserApi.password-policy.Count", 1);
        var result = await userService.GetPasswordPolicy(ct);
        return TypedResults.Ok(result);
    }
}