﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class UploadFileLogConfiguration : IEntityTypeConfiguration<UploadFileLog>
    {
        public void Configure(EntityTypeBuilder<UploadFileLog> entity)
        {
            entity
                .HasNoKey()
                .ToTable("UploadFile_log", "SPL");

            entity.Property(e => e.DtmDateTime)
                .HasColumnType("datetime")
                .HasColumnName("dtmDateTime");
            entity.Property(e => e.IntUserSk)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("intUserSk");
            entity.Property(e => e.StrFileName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strFileName");
            entity.Property(e => e.StrMonth)
                .HasMaxLength(6)
                .IsUnicode(false)
                .HasColumnName("strMonth");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<UploadFileLog> entity);
    }
}
