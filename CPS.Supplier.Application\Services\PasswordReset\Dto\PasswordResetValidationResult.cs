namespace CPS.Supplier.Application.Services.PasswordReset.Dto;

public class PasswordResetValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }

    public static PasswordResetValidationResult Success() => new() { IsValid = true };

    public static PasswordResetValidationResult Failed(string errorMessage) => new() { IsValid = false, ErrorMessage = errorMessage };
}