﻿using CPS.Supplier.Application.Interfaces.Persistence;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage;

namespace CPS.Supplier.Persistence.Data;
public class DatabaseFacadeWrapper : IDatabaseFacade
{
    private readonly DatabaseFacade _databaseFacade;

    public DatabaseFacadeWrapper(DatabaseFacade databaseFacade)
    {
        _databaseFacade = databaseFacade;
    }

    public async Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters)
    {
        return await _databaseFacade.ExecuteSqlRawAsync(sql, parameters);
    }

    public async Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        return await _databaseFacade.BeginTransactionAsync(cancellationToken);
    }
}