﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierVolumeFileMonthConfiguration : IEntityTypeConfiguration<SupplierVolumeFileMonth>
    {
        public void Configure(EntityTypeBuilder<SupplierVolumeFileMonth> entity)
        {
            entity.HasKey(e => e.IntDataFileMonthId).IsClustered(false);

            entity.ToTable("SupplierVolumeFileMonth", "SPL");

            entity.Property(e => e.IntDataFileMonthId).HasColumnName("intDataFileMonthID");
            entity.Property(e => e.IntDataFileId).HasColumnName("intDataFileID");
            entity.Property(e => e.StrMonth)
                .IsRequired()
                .HasMaxLength(5)
                .IsUnicode(false)
                .HasColumnName("strMonth");
            entity.Property(e => e.StrYear)
                .IsRequired()
                .HasMaxLength(4)
                .IsUnicode(false)
                .HasColumnName("strYear");

            entity.HasOne(d => d.IntDataFile).WithMany(p => p.SupplierVolumeFileMonths)
                .HasForeignKey(d => d.IntDataFileId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SupplierVolumeFileMonth_SupplierVolumeFile");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierVolumeFileMonth> entity);
    }
}
