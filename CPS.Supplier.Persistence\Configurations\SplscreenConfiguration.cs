﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SplscreenConfiguration : IEntityTypeConfiguration<Splscreen>
    {
        public void Configure(EntityTypeBuilder<Splscreen> entity)
        {
            entity
                .HasNoKey()
                .ToTable("SPLScreens", "SPL");

            entity.Property(e => e.IntScreenId)
                .ValueGeneratedOnAdd()
                .HasColumnName("intScreenId");
            entity.Property(e => e.StrActionName)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("strActionName");
            entity.Property(e => e.StrModelName)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("strModelName");
            entity.Property(e => e.StrScreenName)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("strScreenName");
            entity.Property(e => e.StrScreenNameFr)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("strScreenName_FR");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<Splscreen> entity);
    }
}
