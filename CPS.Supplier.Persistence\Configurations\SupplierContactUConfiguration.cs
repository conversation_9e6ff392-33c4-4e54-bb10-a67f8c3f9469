﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierContactUConfiguration : IEntityTypeConfiguration<SupplierContactU>
    {
        public void Configure(EntityTypeBuilder<SupplierContactU> entity)
        {
            entity.HasKey(e => e.IntSupplierContactUsId).IsClustered(false);

            entity.ToTable("SupplierContactUs", "SPL");

            entity.Property(e => e.IntSupplierContactUsId).HasColumnName("intSupplierContactUsID");
            entity.Property(e => e.BlnIsCopyMe).HasColumnName("blnIsCopyMe");
            entity.Property(e => e.DttmCreateDate)
                .HasColumnType("datetime")
                .HasColumnName("dttmCreateDate");
            entity.Property(e => e.IntUserSk).HasColumnName("intUserSk");
            entity.Property(e => e.StrCompany)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("strCompany");
            entity.Property(e => e.StrEmail)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strEmail");
            entity.Property(e => e.StrFirstName)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("strFirstName");
            entity.Property(e => e.StrLastName)
                .IsRequired()
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("strLastName");
            entity.Property(e => e.StrMessage)
                .IsUnicode(false)
                .HasColumnName("strMessage");
            entity.Property(e => e.StrPhone)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("strPhone");
            entity.Property(e => e.StrPosition)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("strPosition");
            entity.Property(e => e.StrSubject)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strSubject");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierContactU> entity);
    }
}
