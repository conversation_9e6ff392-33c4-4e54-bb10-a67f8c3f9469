﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class TestConfiguration : IEntityTypeConfiguration<Test>
    {
        public void Configure(EntityTypeBuilder<Test> entity)
        {
            entity
                .HasNoKey()
                .ToTable("test", "SPL");

            entity.Property(e => e.IntDataFileRowId).HasColumnName("intDataFileRowID");
            entity.Property(e => e.IntUserSk)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("intUserSk");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<Test> entity);
    }
}
