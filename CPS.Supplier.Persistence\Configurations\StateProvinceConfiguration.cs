﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class StateProvinceConfiguration : IEntityTypeConfiguration<StateProvince>
    {
        public void Configure(EntityTypeBuilder<StateProvince> entity)
        {
            entity
                .HasNoKey()
                .ToView("StateProvince", "SPL");

            entity.Property(e => e.DttmInsertDate)
                .HasColumnType("datetime")
                .HasColumnName("dttmInsertDate");
            entity.Property(e => e.DttmSourceCreation)
                .HasColumnType("datetime")
                .HasColumnName("dttmSourceCreation");
            entity.Property(e => e.DttmUpdateDate)
                .HasColumnType("datetime")
                .HasColumnName("dttmUpdateDate");
            entity.Property(e => e.IntCountryId).HasColumnName("intCountryID");
            entity.Property(e => e.IntDataSourceId).HasColumnName("intDataSourceID");
            entity.Property(e => e.IntStateProvinceId).HasColumnName("intStateProvinceID");
            entity.Property(e => e.StrAbbreviation)
                .HasMaxLength(5)
                .IsUnicode(false)
                .HasColumnName("strAbbreviation");
            entity.Property(e => e.StrName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strName");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<StateProvince> entity);
    }
}
