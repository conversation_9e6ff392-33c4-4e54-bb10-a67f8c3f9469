﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class ActivityTypeConfiguration : IEntityTypeConfiguration<ActivityType>
    {
        public void Configure(EntityTypeBuilder<ActivityType> entity)
        {
            entity.HasKey(e => e.IntActivityTypeId).IsClustered(false);

            entity.ToTable("ActivityType", "SPL");

            entity.Property(e => e.IntActivityTypeId).HasColumnName("intActivityTypeID");
            entity.Property(e => e.StrName)
                .IsRequired()
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("strName");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<ActivityType> entity);
    }
}
