﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>

namespace CPS.Supplier.Persistence.Configurations
{
    public partial class CpscustomerConfiguration : IEntityTypeConfiguration<Cpscustomer>
    {
        public void Configure(EntityTypeBuilder<Cpscustomer> entity)
        {
            entity
                .HasNoKey()
                .ToView("CPSCustomer", "SPL");

            entity.Property(e => e.CloseDate).HasColumnType("datetime");
            entity.Property(e => e.IntStatusDateId).HasColumnName("intStatusDateID");
            entity.Property(e => e.IntVtrakDivisionId).HasColumnName("intVTrakDivisionID");
            entity.Property(e => e.IntstatusId).HasColumnName("intstatusID");
            entity.Property(e => e.OpenDate).HasColumnType("datetime");
            entity.Property(e => e.StrAddress1)
                .IsRequired()
                .HasMaxLength(56)
                .IsUnicode(false)
                .HasColumnName("strAddress1");
            entity.Property(e => e.StrAddress2)
                .IsRequired()
                .HasMaxLength(56)
                .IsUnicode(false)
                .HasColumnName("strAddress2");
            entity.Property(e => e.StrCassdivision)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strCASSDivision");
            entity.Property(e => e.StrCity)
                .IsRequired()
                .HasMaxLength(42)
                .IsUnicode(false)
                .HasColumnName("strCity");
            entity.Property(e => e.StrClientNumber)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strClientNumber");
            entity.Property(e => e.StrCustomerId)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("strCustomerID");
            entity.Property(e => e.StrName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strName");
            entity.Property(e => e.StrNumber)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strNumber");
            entity.Property(e => e.StrStateProvCode)
                .HasMaxLength(5)
                .IsUnicode(false)
                .HasColumnName("strStateProvCode");
            entity.Property(e => e.StrStateProvince)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strStateProvince");
            entity.Property(e => e.StrZipcode)
                .IsRequired()
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("strZipcode");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<Cpscustomer> entity);
    }
}
