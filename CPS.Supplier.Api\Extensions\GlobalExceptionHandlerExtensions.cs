﻿using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Net;
using System.Text.Json;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Exceptions;
using CPS.Supplier.Application.Services.Common.Dto;
using CPS.Supplier.Application.Services.Localization;
using FluentValidation;
using Microsoft.AspNetCore.Diagnostics;

namespace CPS.Supplier.Api.Extensions
{
    public static class GlobalExceptionHandlerExtensions
    {
        public static void ConfigureGlobalExceptionHandler(this WebApplication app)
        {
            app.UseExceptionHandler(exceptionHandlerApp => exceptionHandlerApp.Run(async context =>
            {
                var exception = GetExceptionFromContext(context);
                if (exception == null)
                {
                    return;
                }

                context.Response.ContentType = "application/json";
                var response = CreateBaseErrorResponse();
                SetResponseStatusAndMessage(context, exception, response, app);
                await context.Response.WriteAsJsonAsync(response);
            }));
        }

        private static Exception? GetExceptionFromContext(HttpContext context)
        {
            var exceptionHandlerFeature = context.Features.Get<IExceptionHandlerFeature>();
            return exceptionHandlerFeature?.Error;
        }

        private static ApiErrorDto CreateBaseErrorResponse()
        {
            return new ApiErrorDto
            {
                Success = false,
                Errors = new List<ErrorDetailDto>() // Fixed MA0007: Added a comma after the last value
            };
        }

        [SuppressMessage("Design", "MA0051:Method is too long", Justification = "<Pending>")]
        private static void SetResponseStatusAndMessage(HttpContext context, Exception exception, ApiErrorDto response, WebApplication app)
        {
            var logger = app.Services.GetRequiredService<ILogger<Program>>();
            var metricsService = app.Services.GetRequiredService<IMetricsService>();
            var configuration = app.Services.GetRequiredService<IConfiguration>();
            var localizationService = app.Services.GetRequiredService<ILocalizationService>();
            bool showDetailed = configuration.GetValue("ShowDetailedExceptionMessages", false);

            switch (exception)
            {
                case ValidationException validationException:
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = localizationService.GetString(ErrorMessages.ValidationFailed);
                    response.Errors = JsonSerializer.Deserialize<List<ErrorDetailDto>>(validationException.Message)!;

                    TrackExceptionMetrics("ValidationException", validationException, context, logger, metricsService);
                    break;

                case BadRequestException badRequestException:
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = badRequestException.Message;

                    TrackExceptionMetrics("BadRequestException", badRequestException, context, logger, metricsService);
                    break;

                case NotFoundException notFoundException:
                    context.Response.StatusCode = (int)HttpStatusCode.NotFound;
                    response.Message = notFoundException.Message;

                    TrackExceptionMetrics("NotFoundException", notFoundException, context, logger, metricsService);
                    break;

                case UnauthorizedException unauthorizedException:
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    response.Message = unauthorizedException.Message;

                    TrackExceptionMetrics("UnauthorizedException", unauthorizedException, context, logger, metricsService);
                    break;

                case ForbiddenException forbiddenException:
                    context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                    response.Message = forbiddenException.Message;

                    TrackExceptionMetrics("ForbiddenException", forbiddenException, context, logger, metricsService);
                    break;

                default:
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    response.Message = showDetailed
                        ? exception.ToString()
                        : localizationService.GetString(ErrorMessages.UnexpectedError);

                    TrackExceptionMetrics("InternalServerError", exception, context, logger, metricsService);
                    break;
            }

            response.StatusCode = context.Response.StatusCode;
        }

        private static void TrackExceptionMetrics(
            string exceptionType,
            Exception exception,
            HttpContext context,
            ILogger<Program> logger,
            IMetricsService metricsService)
        {
            var statusCode = context.Response.StatusCode.ToString(CultureInfo.InvariantCulture);
            var metricName = $"GlobalExceptionHandler.{exceptionType}.Count";
            var eventName = $"GlobalExceptionHandler.{exceptionType}";

            // Log the complete exception with stack trace
            logger.LogError(exception, "Exception occurred in GlobalExceptionHandler: {ExceptionType} with StatusCode: {StatusCode}. Message: {ExceptionMessage}",
                exceptionType, statusCode, exception.Message);

            logger.LogDebug("Tracking metrics for exception: {ExceptionType} with StatusCode: {StatusCode}", exceptionType, statusCode);

            // Track exception count metric
            metricsService.TrackMetric(metricName, 1);
            logger.LogDebug("Successfully tracked metric: {MetricName}", metricName);

            // Track exception event with properties
            metricsService.TrackEvent(eventName, new Dictionary<string, string>(StringComparer.Ordinal)
            {
                ["Message"] = exception.Message,
                ["StatusCode"] = statusCode
            });
            logger.LogDebug("Successfully tracked event: {EventName} for exception: {ExceptionType}", eventName, exceptionType);
        }
    }

}