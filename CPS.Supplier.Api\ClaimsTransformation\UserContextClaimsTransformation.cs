using System.Globalization;
using System.Security.Claims;
using System.Text.Json;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.UserContext.Contracts;
using CPS.Supplier.Application.Services.UserContext.Dto;
using Microsoft.AspNetCore.Authentication;

namespace CPS.Supplier.Api.ClaimsTransformation;

public class UserContextClaimsTransformation(
    IUserContextService userContextService) : IClaimsTransformation
{
    public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
    {
        if (principal.Identity?.IsAuthenticated != true)
        {
            return principal;
        }

        if (principal.Claims.Any(c => string.Equals(c.Type, CustomClaimTypes.UserId, StringComparison.Ordinal)))
        {
            return principal;
        }

        var userId = GetUserIdFromJwtToken(principal);

        if (userId <= 0)
        {
            throw new Application.Exceptions.ForbiddenException("Unable to extract userId from JWT token for user");
        }

        var userContext = await GetUserContextWithCaching(userId);
        if (userContext == null)
        {
            throw new Application.Exceptions.ForbiddenException($"No user context found for userId: {userId}");
        }

        // Create a new claims identity with enhanced claims
        var claimsIdentity = new ClaimsIdentity("UserContextTransformation");
        AddUserContextClaims(claimsIdentity, userContext);

        // Add the new identity to the principal
        principal.AddIdentity(claimsIdentity);

        return principal;
    }

    private static int GetUserIdFromJwtToken(ClaimsPrincipal principal)
    {
        var samAccountName = principal.FindFirst("sAMAccountName");
        if (!string.IsNullOrEmpty(samAccountName?.Value)
            && int.TryParse(samAccountName.Value, CultureInfo.InvariantCulture, out int userId))
        {
            return userId;
        }

        return 0;
    }

    private async Task<UserContextDto?> GetUserContextWithCaching(int userId)
    {
        var userContext = await userContextService.GetUserContext(userId);

        // Implement caching logic here by referring to the first commit for this file

        return userContext;
    }

    private static void AddUserContextClaims(ClaimsIdentity identity, UserContextDto userContext)
    {
        identity.AddClaim(new Claim(CustomClaimTypes.UserId, userContext.UserId.ToString(CultureInfo.InvariantCulture)));
        identity.AddClaim(new Claim(CustomClaimTypes.Username, userContext.Username));
        identity.AddClaim(new Claim(CustomClaimTypes.IsSuperAdmin, userContext.IsSuperAdmin.ToString()));

        foreach (var role in userContext.Roles)
        {
            identity.AddClaim(new Claim(ClaimTypes.Role, role));
        }

        // Serialize module permissions as JSON, normalizing both module names and permission names by removing spaces
        if (userContext.ModulePermissions.Count > 0)
        {
            var modulePermissionsJson = JsonSerializer.Serialize(userContext.ModulePermissions);
            identity.AddClaim(new Claim(CustomClaimTypes.ModulePermissions, modulePermissionsJson));
        }

        if (userContext.SupplierId.HasValue)
        {
            identity.AddClaim(new Claim(CustomClaimTypes.SupplierId, userContext.SupplierId.Value.ToString(CultureInfo.InvariantCulture)));
        }

        if (!string.IsNullOrEmpty(userContext.SupplierName))
        {
            identity.AddClaim(new Claim(CustomClaimTypes.SupplierName, userContext.SupplierName));
        }

        if (userContext.FacilityId.HasValue)
        {
            identity.AddClaim(new Claim(CustomClaimTypes.FacilityId, userContext.FacilityId.Value.ToString(CultureInfo.InvariantCulture)));
        }

        if (userContext.LanguageId > 0)
        {
            identity.AddClaim(new Claim(CustomClaimTypes.LanguageId, userContext.LanguageId.ToString(CultureInfo.InvariantCulture)));
        }

        // Add additional claims if any
        foreach (var additionalClaim in userContext.AdditionalClaims)
        {
            identity.AddClaim(new Claim($"custom:{additionalClaim.Key}", additionalClaim.Value));
        }
    }
}