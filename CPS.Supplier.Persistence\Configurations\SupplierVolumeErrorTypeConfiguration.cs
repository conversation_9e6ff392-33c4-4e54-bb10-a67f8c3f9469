﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierVolumeErrorTypeConfiguration : IEntityTypeConfiguration<SupplierVolumeErrorType>
    {
        public void Configure(EntityTypeBuilder<SupplierVolumeErrorType> entity)
        {
            entity.HasKey(e => e.IntErrorTypeId).IsClustered(false);

            entity.ToTable("SupplierVolumeErrorTypes", "SPL");

            entity.Property(e => e.IntErrorTypeId).HasColumnName("intErrorTypeID");
            entity.Property(e => e.StrDescription)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("strDescription");
            entity.Property(e => e.StrDescriptionFr)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("strDescriptionFR");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierVolumeErrorType> entity);
    }
}
