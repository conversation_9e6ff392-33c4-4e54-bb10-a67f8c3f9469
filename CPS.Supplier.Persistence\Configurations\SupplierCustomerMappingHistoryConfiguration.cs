﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierCustomerMappingHistoryConfiguration : IEntityTypeConfiguration<SupplierCustomerMappingHistory>
    {
        public void Configure(EntityTypeBuilder<SupplierCustomerMappingHistory> entity)
        {
            entity.HasKey(e => e.IntSupplierCustomerMappingHistoryId)
                .HasName("PK_SupplierCustomerMappingHistory")
                .IsClustered(false);

            entity.ToTable("Supplier_Customer_Mapping_History", "SPL");

            entity.Property(e => e.IntSupplierCustomerMappingHistoryId).HasColumnName("intSupplierCustomerMappingHistoryID");
            entity.Property(e => e.DttmCreateDate)
                .HasColumnType("datetime")
                .HasColumnName("dttmCreateDate");
            entity.Property(e => e.IntSupplierCustomerMappingId).HasColumnName("intSupplierCustomerMappingID");
            entity.Property(e => e.IntSupplierId).HasColumnName("intSupplierID");
            entity.Property(e => e.IntUserSk).HasColumnName("intUserSK");
            entity.Property(e => e.StrAracpscustomerId)
                .IsRequired()
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("strARACPSCustomerID");
            entity.Property(e => e.StrSupplierCustomerId)
                .IsRequired()
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("strSupplierCustomerID");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierCustomerMappingHistory> entity);
    }
}
