﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class MonthNameConfiguration : IEntityTypeConfiguration<MonthName>
    {
        public void Configure(EntityTypeBuilder<MonthName> entity)
        {
            entity
                .HasNoKey()
                .ToTable("MonthNames", "SPL");

            entity.Property(e => e.IntMonthNum).HasColumnName("intMonthNum");
            entity.Property(e => e.StrMonthNameE)
                .HasMaxLength(20)
                .HasColumnName("strMonthNameE");
            entity.Property(e => e.StrMonthNameF)
                .HasMaxLength(20)
                .HasColumnName("strMonthNameF");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<MonthName> entity);
    }
}
