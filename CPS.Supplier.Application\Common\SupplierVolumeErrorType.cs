namespace CPS.Supplier.Application.Common;

/// <summary>
/// Defines error types for supplier volume file processing and validation.
/// Used for comprehensive error classification during CSV file processing.
/// </summary>
public enum SupplierVolumeErrorType
{
    ColumnsDoNotMatch = 1,
    MismatchInDataType = 2,
    CharacterLimitExceeded = 3,
    MandatoryFieldMissing = 4,
    SqlError = 5,
    DuplicateCustomerRecord = 6,
    DataOlderThan12Months = 7,
    InvalidRecordNumber = 8,
    CurrentOrFutureMonthData = 9,
    InvalidProvince = 10,
    FileHasNoData = 11,
    InvalidDateFormat = 12,
    DuplicateRecordNumber = 13,
    InvalidCustomerId = 14,
    InvalidAmount = 15
}