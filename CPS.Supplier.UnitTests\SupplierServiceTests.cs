using CPS.Supplier.Application.Interfaces.Persistence;
using CPS.Supplier.Application.Services.Supplier;
using CPS.Supplier.Application.Services.UserContext.Contracts;
using CPS.Supplier.Domain.Entities;

namespace CPS.Supplier.UnitTests
{
    public class SupplierServiceTests
    {
        private readonly IApplicationDbContext _dbContext;
        private readonly ICurrentUserService _currentUserService;
        private readonly SupplierService _supplierService;

        public SupplierServiceTests()
        {
            // Setup mock DbContext
            _dbContext = Substitute.For<IApplicationDbContext>();
            // Setup mock CurrentUserService
            _currentUserService = Substitute.For<ICurrentUserService>();

            // Create the actual service with simplified constructor
            _supplierService = new SupplierService(_dbContext, _currentUserService);
        }

        [Fact]
        public async Task GetSuppliersWithUserCountShouldReturnCorrectSuppliers()
        {
            // Arrange
            var suppliers = new List<Domain.Entities.Supplier>
            {
                new Domain.Entities.Supplier
                {
                    IntSupplierId = 1,
                    StrName = "AABBOTT LABORATORIES",
                    StrStatus = "A",
                    UserCount = 5,
                    SrcVendorId = "11",
                    IntStatusId = 12
                },
                new Domain.Entities.Supplier
                {
                    IntSupplierId = 2,
                    StrName = "B & C MEATS",
                    StrStatus = "I",
                    UserCount = 40,
                    SrcVendorId = "22",
                    IntStatusId = 12
                },
                new Domain.Entities.Supplier
                {
                    IntSupplierId = 3,
                    StrName = "MEATS",
                    StrStatus = "A",
                    UserCount = 3,
                    SrcVendorId = "43",
                    IntStatusId = 10 // should be excluded
                }
            };

            var supplierDbSet = suppliers.AsQueryable().BuildMockDbSet();

            _dbContext.GetDbSet<Domain.Entities.Supplier>().Returns(supplierDbSet);

            // Act
            var result = await _supplierService.GetSuppliersWithUserCount(CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(2); // Only active suppliers (IntStatusId = 12)
            var firstSupplier = result[0];
            firstSupplier.Name.ShouldBe("AABBOTT LABORATORIES");
            firstSupplier.UserCount.ShouldBe(5);
            firstSupplier.VendorId.ShouldBe("11");
            var secondSupplier = result[1];
            secondSupplier.Name.ShouldBe("B & C MEATS");
            secondSupplier.UserCount.ShouldBe(40);
            secondSupplier.VendorId.ShouldBe("22");
        }

        [Fact]
        public async Task GetSupplierUsersShouldReturnSupplierUsers()
        {
            // Arrange
            var users = new List<VwSupplierUser>
        {
            new VwSupplierUser
            {
                IntUserSk = 1,
                StrFirstName = "Supplier",
                StrLastName = "User",
                StrEmailAddress = "<EMAIL>",
                StrUsername = "supplier1",
                IntSupplierId = 123,
                StrCode = "Active",
                IntLanguageId = 1,
                IntStatusId = 1
            }
        };

            var dbSet = users.AsQueryable().BuildMockDbSet();
            _dbContext.GetDbSet<VwSupplierUser>().Returns(dbSet);
            _currentUserService.LanguageId.Returns(1);

            // Act
            var result = await _supplierService.GetSupplierUsers(123, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            var supplierUser = result[0];
            supplierUser.IntUserSk.ShouldBe(1);
            supplierUser.FirstName.ShouldBe("Supplier");
            supplierUser.Username.ShouldBe("supplier1");
            supplierUser.Email.ShouldBe("<EMAIL>");
            supplierUser.Status.ShouldBe("Active");
        }
    }
}