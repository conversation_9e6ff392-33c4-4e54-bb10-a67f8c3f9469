namespace CPS.Supplier.Application.Common;

/// <summary>
/// Resource keys for error messages.
/// Extracted from LocalizationConstants for Microsoft .NET 8 standards compliance.
/// </summary>
public static class ErrorMessages
{
    public const string RequestFailed = "ErrorMessages_RequestFailed";
    public const string ResourceNotFound = "ErrorMessages_ResourceNotFound";
    public const string AccessForbidden = "ErrorMessages_AccessForbidden";
    public const string Unauthorized = "ErrorMessages_Unauthorized";
    public const string ValidationFailed = "ErrorMessages_ValidationFailed";
    public const string TechnicalError = "ErrorMessages_TechnicalError";
    public const string UnexpectedError = "ErrorMessages_UnexpectedError";

    // File Operation Error Messages
    public const string InvalidFileStatus = "ErrorMessages_InvalidFileStatus";
    public const string UserCreationFailed = "ErrorMessages_UserCreationFailed";
    public const string UnknownField = "ErrorMessages_UnknownField";
    public const string InsufficientPermissions = "ErrorMessages_InsufficientPermissions";
    public const string SupplierAccessOnly = "ErrorMessages_SupplierAccessOnly";
}