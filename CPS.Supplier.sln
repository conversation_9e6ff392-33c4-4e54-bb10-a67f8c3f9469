﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.34928.147
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A819384C-FB43-46F9-89FF-B6F78CDFA751}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{127073A9-5F3A-4D90-A363-A82756D6B1C3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CPS.Supplier.Domain", "CPS.Supplier.Domain\CPS.Supplier.Domain.csproj", "{FC98965D-2E62-40A8-99C3-8BA76C9E257F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{5612BBB1-669A-4FEB-98E9-90799CD55F4A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CPS.Supplier.Persistence", "CPS.Supplier.Persistence\CPS.Supplier.Persistence.csproj", "{D0C2DBD3-8E91-43F5-8CBB-39CF846E2AAD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CPS.Supplier.Application", "CPS.Supplier.Application\CPS.Supplier.Application.csproj", "{6A09CD3B-6AF8-4918-922C-730E620025A1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CPS.Supplier.Infrastructure", "CPS.Supplier.Infrastructure\CPS.Supplier.Infrastructure.csproj", "{BE30ACBD-EB7C-4069-BA1E-8D26A82A91E0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "API", "API", "{505D337F-0284-452C-8599-61090BC2E8F7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CPS.Supplier.Api", "CPS.Supplier.Api\CPS.Supplier.Api.csproj", "{8B5BDBFB-3FCC-4EE7-9D11-81FE573B16EC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		.gitignore = .gitignore
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		README.md = README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{261417D1-5F81-4C8A-88C9-84D415445CFD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CPS.Supplier.UnitTests", "CPS.Supplier.UnitTests\CPS.Supplier.UnitTests.csproj", "{8633B11D-98BB-19BF-5E5E-C06711F199F0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CPS.Supplier.ArchitectureTests", "CPS.Supplier.ArchitectureTests\CPS.Supplier.ArchitectureTests.csproj", "{267C4C59-1BFD-4B8B-82F4-62AC96228F3F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AzureFunction", "AzureFunction", "{A551D555-8B2F-4351-AC9D-BBC5BCCF5079}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CPS.Supplier.Functions", "CPS.Supplier.Functions\CPS.Supplier.Functions.csproj", "{3A2899C1-6816-4D16-B3D7-6BA09414BF6E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FC98965D-2E62-40A8-99C3-8BA76C9E257F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC98965D-2E62-40A8-99C3-8BA76C9E257F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FC98965D-2E62-40A8-99C3-8BA76C9E257F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC98965D-2E62-40A8-99C3-8BA76C9E257F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0C2DBD3-8E91-43F5-8CBB-39CF846E2AAD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0C2DBD3-8E91-43F5-8CBB-39CF846E2AAD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0C2DBD3-8E91-43F5-8CBB-39CF846E2AAD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0C2DBD3-8E91-43F5-8CBB-39CF846E2AAD}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A09CD3B-6AF8-4918-922C-730E620025A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A09CD3B-6AF8-4918-922C-730E620025A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A09CD3B-6AF8-4918-922C-730E620025A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A09CD3B-6AF8-4918-922C-730E620025A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE30ACBD-EB7C-4069-BA1E-8D26A82A91E0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE30ACBD-EB7C-4069-BA1E-8D26A82A91E0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE30ACBD-EB7C-4069-BA1E-8D26A82A91E0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE30ACBD-EB7C-4069-BA1E-8D26A82A91E0}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B5BDBFB-3FCC-4EE7-9D11-81FE573B16EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B5BDBFB-3FCC-4EE7-9D11-81FE573B16EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B5BDBFB-3FCC-4EE7-9D11-81FE573B16EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B5BDBFB-3FCC-4EE7-9D11-81FE573B16EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{8633B11D-98BB-19BF-5E5E-C06711F199F0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8633B11D-98BB-19BF-5E5E-C06711F199F0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8633B11D-98BB-19BF-5E5E-C06711F199F0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8633B11D-98BB-19BF-5E5E-C06711F199F0}.Release|Any CPU.Build.0 = Release|Any CPU
		{267C4C59-1BFD-4B8B-82F4-62AC96228F3F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{267C4C59-1BFD-4B8B-82F4-62AC96228F3F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{267C4C59-1BFD-4B8B-82F4-62AC96228F3F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{267C4C59-1BFD-4B8B-82F4-62AC96228F3F}.Release|Any CPU.Build.0 = Release|Any CPU
		{3A2899C1-6816-4D16-B3D7-6BA09414BF6E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3A2899C1-6816-4D16-B3D7-6BA09414BF6E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3A2899C1-6816-4D16-B3D7-6BA09414BF6E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3A2899C1-6816-4D16-B3D7-6BA09414BF6E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{127073A9-5F3A-4D90-A363-A82756D6B1C3} = {A819384C-FB43-46F9-89FF-B6F78CDFA751}
		{FC98965D-2E62-40A8-99C3-8BA76C9E257F} = {127073A9-5F3A-4D90-A363-A82756D6B1C3}
		{5612BBB1-669A-4FEB-98E9-90799CD55F4A} = {A819384C-FB43-46F9-89FF-B6F78CDFA751}
		{D0C2DBD3-8E91-43F5-8CBB-39CF846E2AAD} = {5612BBB1-669A-4FEB-98E9-90799CD55F4A}
		{6A09CD3B-6AF8-4918-922C-730E620025A1} = {127073A9-5F3A-4D90-A363-A82756D6B1C3}
		{BE30ACBD-EB7C-4069-BA1E-8D26A82A91E0} = {5612BBB1-669A-4FEB-98E9-90799CD55F4A}
		{505D337F-0284-452C-8599-61090BC2E8F7} = {A819384C-FB43-46F9-89FF-B6F78CDFA751}
		{8B5BDBFB-3FCC-4EE7-9D11-81FE573B16EC} = {505D337F-0284-452C-8599-61090BC2E8F7}
		{8633B11D-98BB-19BF-5E5E-C06711F199F0} = {261417D1-5F81-4C8A-88C9-84D415445CFD}
		{267C4C59-1BFD-4B8B-82F4-62AC96228F3F} = {261417D1-5F81-4C8A-88C9-84D415445CFD}
		{A551D555-8B2F-4351-AC9D-BBC5BCCF5079} = {A819384C-FB43-46F9-89FF-B6F78CDFA751}
		{3A2899C1-6816-4D16-B3D7-6BA09414BF6E} = {A551D555-8B2F-4351-AC9D-BBC5BCCF5079}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E70C3191-0EF3-4681-89AA-C0A1EDD78F75}
	EndGlobalSection
EndGlobal
