using System.ComponentModel.DataAnnotations;

namespace CPS.Supplier.Application.Services.Common.Dto;

/// <summary>
/// Base class for all paged queries with sorting capabilities
/// </summary>
public class PagedBaseQuery
{
    /// <summary>
    /// Gets or sets the page number (1-based)
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Gets or sets the number of items per page (max 100 by default)
    /// </summary>
    [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Gets or sets the property name to sort by
    /// </summary>
    public virtual string SortBy { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the sort direction ("asc" or "desc")
    /// </summary>
    public string SortOrder { get; set; } = "asc";

    /// <summary>
    /// Gets a value indicating whether the sort order is descending
    /// </summary>
    public bool IsDescendingOrder => SortOrder.Equals("desc", StringComparison.OrdinalIgnoreCase);
}