namespace CPS.Supplier.Application.Common.Configuration;

/// <summary>
/// Constants for configuration keys to avoid magic strings
/// </summary>
public static class ConfigurationKeys
{
    // Connection Strings
    public const string ConnectionStrings = "ConnectionStrings";
    public const string SupplierConnectionString = "ConnectionStrings:SupplierConnectionString";

    // Azure Storage
    public const string AzureStorage = "AzureStorage";
    public const string AzureStorageConnectionString = "AzureStorage:ConnectionString";
    public const string AzureStorageBlobContainer = "AzureStorage:BlobContainerName";
    public const string AzureStorageQueueName = "AzureStorage:QueueName";
    public const string AzureStorageArchivedContainer = "AzureStorage:ArchivedFilesContainer";

    // Auth0
    public const string Auth0 = "Auth0";
    public const string Auth0Domain = "Auth0:Domain";
    public const string Auth0Audience = "Auth0:Audience";
    public const string Auth0ClientId = "Auth0:ClientId";
    public const string Auth0ClientSecret = "Auth0:ClientSecret";
    public const string Auth0TokenUrl = "Auth0:TokenUrl";

    // KeyVault
    public const string KeyVault = "KeyVault";
    public const string KeyVaultEnabled = "KeyVault:Enabled";
    public const string KeyVaultEndpoint = "KeyVault:Endpoint";

    // CORS
    public const string Cors = "Cors";
    public const string CorsAllowedOrigins = "Cors:AllowedOrigins";

    // Application Insights
    public const string ApplicationInsights = "ApplicationInsights";
    public const string ApplicationInsightsConnectionString = "ApplicationInsights:ConnectionString";
    public const string EnabledApplicationInsightsLogger = "EnabledApplicationInsightsLogger";
    public const string EnabledApplicationInsightsMetrics = "EnabledApplicationInsightsMetrics";

    // File Storage
    public const string FileStorage = "FileStorage";
    public const string FileStorageUploadPath = "FileStorage:UploadPath";
    public const string FileStorageOutputPath = "FileStorage:OutputPath";
    public const string FileStorageVtrakPath = "FileStorage:VtrakPath";

    // Administration API
    public const string AdministrationApi = "AdministrationApi";
    public const string AdministrationApiBaseUrl = "AdministrationApi:BaseUrl";

    // General
    public const string ShowDetailedExceptionMessages = "ShowDetailedExceptionMessages";
    public const string AllowedHosts = "AllowedHosts";

    // Azure Functions specific
    public const string AzureWebJobsStorage = "AzureWebJobsStorage";
    public const string FunctionsWorkerRuntime = "FUNCTIONS_WORKER_RUNTIME";
}