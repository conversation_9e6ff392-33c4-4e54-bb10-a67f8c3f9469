namespace CPS.Supplier.Application.Common;

/// <summary>
/// Resource keys for validation messages.
/// Extracted from LocalizationConstants for Microsoft .NET 8 standards compliance.
/// </summary>
public static class ValidationMessages
{
    public const string FileRequired = "ValidationMessages_FileRequired";
    public const string InvalidCsvFormat = "ValidationMessages_InvalidCsvFormat";
    public const string ValidSupplierIdRequired = "ValidationMessages_ValidSupplierIdRequired";
    public const string SupplierNotFound = "ValidationMessages_SupplierNotFound";
    public const string SupplierNotActive = "ValidationMessages_SupplierNotActive";
    public const string SameMonthUploadExists = "ValidationMessages_SameMonthUploadExists";
    public const string InvalidFileSize = "ValidationMessages_InvalidFileSize";
    public const string InvalidFileExtension = "ValidationMessages_InvalidFileExtension";
    public const string FileNameTooLong = "ValidationMessages_FileNameTooLong";
    public const string FileNameRequired = "ValidationMessages_FileNameRequired";
    public const string BlobPathRequired = "ValidationMessages_BlobPathRequired";
    public const string ContainerNameRequired = "ValidationMessages_ContainerNameRequired";
    public const string ProcessingMonthRequiredWithFormat = "ValidationMessages_ProcessingMonthRequiredWithFormat";
    public const string UserSKRequired = "ValidationMessages_UserSKRequired";
    public const string SupplierIdRequired = "ValidationMessages_SupplierIdRequired";
    public const string ProcessingMonthInvalidFormat = "ValidationMessages_ProcessingMonthInvalidFormat";

    // File Operation Validation Messages
    public const string MappingRequired = "ValidationMessages_MappingRequired";
    public const string CompleteFileRequiresMappedRecords = "ValidationMessages_CompleteFileRequiresMappedRecords";
    public const string InvalidRecordStatus = "ValidationMessages_InvalidRecordStatus";
    public const string CustomerNotFound = "ValidationMessages_CustomerNotFound";
    public const string OnlyInvalidRecordsCanBeMarkedValid = "ValidationMessages_OnlyInvalidRecordsCanBeMarkedValid";
}