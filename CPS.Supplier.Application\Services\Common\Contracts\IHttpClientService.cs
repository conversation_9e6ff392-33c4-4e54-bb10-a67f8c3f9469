namespace CPS.Supplier.Application.Services.Common.Contracts;

public interface IHttpClientService
{
    Task<TResponse?> GetAsync<TResponse>(string endpoint, CancellationToken cancellationToken, bool allowAnonymous = false);

    Task<TResponse?> PostAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken, bool allowAnonymous = false)
        where TRequest : class;
    Task<TResponse?> PutAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken, bool allowAnonymous = false)
        where TRequest : class;
}