﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable

namespace CPS.Supplier.Domain.Entities;

public partial class SupplierVolumeFileDatum
{
    public int IntDataFileRowId { get; set; }

    public int IntRecordNo { get; set; }

    public decimal DecTotalVolume { get; set; }

    public int IntDataFileId { get; set; }

    public int IntUserSk { get; set; }

    public int IntDataStatusId { get; set; }

    public string StrSupplierCustomerId { get; set; }

    public string StrAracpscustomerId { get; set; }

    public string StrCustFileTitle { get; set; }

    public string StrCustFileLocation { get; set; }

    public string StrCustFileStaddress1 { get; set; }

    public string StrCustFileStaddress2 { get; set; }

    public string StrCustFileCity { get; set; }

    public string StrCustFileProvinceCode { get; set; }

    public string StrCustFilePostCode { get; set; }

    public DateTime? DttmTransactionMonth { get; set; }

    public virtual SupplierVolumeDataStatus IntDataStatus { get; set; }
}
