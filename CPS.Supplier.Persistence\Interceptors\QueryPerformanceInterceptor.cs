﻿using System.Data.Common;
using System.Diagnostics;
using CPS.Supplier.Application.Common;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;

namespace CPS.Supplier.Persistence.Interceptors;
public class QueryPerformanceInterceptor(
    ILogger<QueryPerformanceInterceptor> logger,
    IMetricsService metricsService) : DbCommandInterceptor
{
    public override ValueTask<DbDataReader> ReaderExecutedAsync(
        DbCommand command,
        CommandExecutedEventData eventData,
        DbDataReader result,
        CancellationToken cancellationToken = default)
    {
        LogCommandExecuted(command, eventData, nameof(ReaderExecutedAsync));
        return base.ReaderExecutedAsync(command, eventData, result, cancellationToken);
    }

    public override DbDataReader ReaderExecuted(
        DbCommand command,
        CommandExecutedEventData eventData,
        DbDataReader result)
    {
        LogCommandExecuted(command, eventData, nameof(ReaderExecuted));
        return base.ReaderExecuted(command, eventData, result);
    }

    public override ValueTask<int> NonQueryExecutedAsync(
        DbCommand command,
        CommandExecutedEventData eventData,
        int result,
        CancellationToken cancellationToken = default)
    {
        LogCommandExecuted(command, eventData, nameof(NonQueryExecutedAsync));
        return base.NonQueryExecutedAsync(command, eventData, result, cancellationToken);
    }

    public override int NonQueryExecuted(
        DbCommand command,
        CommandExecutedEventData eventData,
        int result)
    {
        LogCommandExecuted(command, eventData, nameof(NonQueryExecuted));
        return base.NonQueryExecuted(command, eventData, result);
    }

    public override ValueTask<object?> ScalarExecutedAsync(
        DbCommand command,
        CommandExecutedEventData eventData,
        object? result,
        CancellationToken cancellationToken = default)
    {
        LogCommandExecuted(command, eventData, nameof(ScalarExecutedAsync));
        return base.ScalarExecutedAsync(command, eventData, result, cancellationToken);
    }

    public override object? ScalarExecuted(
        DbCommand command,
        CommandExecutedEventData eventData,
        object? result)
    {
        LogCommandExecuted(command, eventData, nameof(ScalarExecuted));
        return base.ScalarExecuted(command, eventData, result);
    }

    private void LogCommandExecuted(DbCommand command, CommandExecutedEventData eventData, string executionType)
    {
        var elapsed = eventData.Duration;
        var elapsedMs = elapsed.TotalMilliseconds;
        var commandText = command.CommandText;
        var queryType = GetQueryType(commandText);
        var sanitizedQuery = SanitizeQuery(commandText);

        logger.LogDebug("Database {ExecutionType} query started for {QueryType}", executionType, queryType);

        logger.LogInformation("Database {QueryType} query executed in {ElapsedMs}ms via {ExecutionType}. Query: {Query}",
            queryType, elapsedMs, executionType, sanitizedQuery);

        TrackQueryMetrics(queryType, elapsedMs, executionType);

        AddTracingTags(elapsedMs, queryType, executionType);
    }

    private void TrackQueryMetrics(string queryType, double elapsedMs, string executionType)
    {
        logger.LogDebug("Tracking database metrics for {QueryType} query - Duration: {ElapsedMs}ms", queryType, elapsedMs);

        var properties = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            ["QueryType"] = queryType,
            ["ExecutionType"] = executionType
        };

        metricsService.TrackMetric("Database.Queries.Duration", elapsedMs, properties);
        logger.LogDebug("Successfully tracked database query duration: {ElapsedMs}ms", elapsedMs);

        metricsService.TrackMetric($"Database.QueryType.{queryType}", 1, properties);
        logger.LogDebug("Successfully tracked database query type metric: {QueryType}", queryType);

    }

    private static void AddTracingTags(double elapsedMs, string queryType, string executionType)
    {
        using var activity = Activity.Current;
        activity?.SetTag("db.execution_time_ms", elapsedMs);
        activity?.SetTag("db.query_type", queryType);
        activity?.SetTag("db.execution_type", executionType);
    }

    private static string SanitizeQuery(string query)
    {
        if (string.IsNullOrWhiteSpace(query))
            return "Empty query";

        // SECURITY: Remove parameter values and limit length
        var lines = query.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        var firstLine = lines.FirstOrDefault()?.Trim() ?? query;

        return firstLine.Length > 200 ? firstLine[..200] + "... (truncated)" : firstLine;
    }

    private static string GetQueryType(string query)
    {
        if (string.IsNullOrWhiteSpace(query))
            return "UNKNOWN";

        var trimmedQuery = query.TrimStart().ToUpperInvariant();
        return trimmedQuery switch
        {
            var q when q.StartsWith("SELECT", StringComparison.Ordinal) => "SELECT",
            var q when q.StartsWith("INSERT", StringComparison.Ordinal) => "INSERT",
            var q when q.StartsWith("UPDATE", StringComparison.Ordinal) => "UPDATE",
            var q when q.StartsWith("DELETE", StringComparison.Ordinal) => "DELETE",
            var q when q.StartsWith("EXEC", StringComparison.Ordinal) || q.StartsWith("CALL", StringComparison.Ordinal) => "PROCEDURE",
            var q when q.StartsWith("CREATE", StringComparison.Ordinal) => "CREATE",
            var q when q.StartsWith("DROP", StringComparison.Ordinal) => "DROP",
            var q when q.StartsWith("ALTER", StringComparison.Ordinal) => "ALTER",
            var q when q.StartsWith("TRUNCATE", StringComparison.Ordinal) => "TRUNCATE",
            _ => "OTHER"
        };
    }
}