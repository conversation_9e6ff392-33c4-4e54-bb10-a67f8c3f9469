﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class ActivityTrackingConfiguration : IEntityTypeConfiguration<ActivityTracking>
    {
        public void Configure(EntityTypeBuilder<ActivityTracking> entity)
        {
            entity.HasKey(e => e.IntActivityTrackingId).IsClustered(false);

            entity.ToTable("ActivityTracking", "SPL");

            entity.Property(e => e.IntActivityTrackingId).HasColumnName("intActivityTrackingID");
            entity.Property(e => e.DttmModified)
                .HasColumnType("datetime")
                .HasColumnName("dttmModified");
            entity.Property(e => e.IntActivityTypeId).HasColumnName("intActivityTypeID");
            entity.Property(e => e.IntDataFileId).HasColumnName("intDataFileID");
            entity.Property(e => e.IntSubjectId).HasColumnName("intSubjectID");
            entity.Property(e => e.IntUserSk).HasColumnName("intUserSK");
            entity.Property(e => e.StrDetails)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strDetails");
            entity.Property(e => e.StrNewValue)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strNewValue");
            entity.Property(e => e.StrOldValue)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strOldValue");

            entity.HasOne(d => d.IntActivityType).WithMany(p => p.ActivityTrackings)
                .HasForeignKey(d => d.IntActivityTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ActivityTracking_ActivityType");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<ActivityTracking> entity);
    }
}
