using System.Globalization;
using System.Text;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Common.Configuration;
using CPS.Supplier.Application.Exceptions;
using CPS.Supplier.Application.Interfaces.Infrastructure;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Application.Services.DataFile.Commands;
using CPS.Supplier.Application.Services.DataFile.Contracts;
using CPS.Supplier.Application.Services.DataFile.Dto;
using CPS.Supplier.Application.Services.Localization;
using CPS.Supplier.Application.Services.UserContext.Contracts;
using CPS.Supplier.Application.Services.VolumeFileProcess.Model;
using CPS.Supplier.Domain.Entities;
using FuzzySharp;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using SupplierVolumeErrorTypeEnum = CPS.Supplier.Application.Common.SupplierVolumeErrorType;

namespace CPS.Supplier.Application.Services.DataFile
{
    public class DataFileService(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IAzureStorageService blobService,
        IQueueService queueService,
        ILocalizationService localizationService,
        IOptions<AzureStorageSettings> storageOptions,
        IModelValidationService modelValidationService) : IDataFileService
    {
        private readonly AzureStorageSettings _storageOptions = storageOptions.Value;

        public async Task UploadFileAsync(UploadFileCommand command)
        {
            await modelValidationService.ValidateAsync(command);

            var effectiveSupplierId = DetermineEffectiveSupplierId(command.SupplierId)
                                        ?? throw new BadRequestException(localizationService.GetString(ValidationMessages.SupplierNotFound));

            var effectiveUserId = await DetermineEffectiveUserIdAsync(command.SupplierId);

            if (!currentUserService.SupplierId.HasValue && !effectiveUserId.HasValue)
                throw new BadRequestException(localizationService.GetString(ValidationMessages.SupplierNotActive));

            var processingMonth = DetermineProcessingMonth();

            // Validate upload eligibility with all business rules
            await ValidateUploadEligibilityAsync(effectiveSupplierId, processingMonth);

            await ProcessFileUploadAsync(command.File, effectiveSupplierId, processingMonth, effectiveUserId!.Value);
        }

        public async Task<IReadOnlyList<DataFileDto>> GetDataFilesAsync(CancellationToken ct)
        {

            var effectiveSupplierId = currentUserService.SupplierId;
            var cutoffDate = DateTime.Now.AddMonths(-12);
            var dbFiles = await GetDataFilesFromDatabaseAsync(effectiveSupplierId, cutoffDate, ct);
            var blobFiles = await GetBlobFilesAsync(effectiveSupplierId, cutoffDate, ct);
            var mergedFiles = await MergeWithBlobFilesAsync(dbFiles, blobFiles, ct);

            return mergedFiles;
        }

        public async Task<FileErrorSummaryDto> GetFileErrorSummaryAsync(int fileId, CancellationToken ct)
        {
            var file = await context.GetDbSet<SupplierVolumeFile>()
                .Include(f => f.IntFileStatus)
                .FirstOrDefaultAsync(f => f.IntDataFileId == fileId, ct)
                ?? throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

            var errorData = await context.GetDbSet<SupplierVolumeFileError>()
                .Include(e => e.IntErrorType)
                .Where(e => e.IntDataFileId == fileId)
                .ToListAsync(ct);

            return new FileErrorSummaryDto
            {
                FileName = ExtractOriginalFileName(file.StrFileName ?? string.Empty),
                Month = file.StrMonth ?? string.Empty,
                Year = file.StrYear ?? string.Empty,
                Status = GetLocalizedStatus(file),
                Errors = errorData.Select(CreateErrorDetail)
            };
        }

        private FileErrorDetailDto CreateErrorDetail(SupplierVolumeFileError error)
        {
            var isSqlError = error.IntErrorTypeId == (int)SupplierVolumeErrorTypeEnum.SqlError;
            var errorMessage = isSqlError ? localizationService.GetString(ErrorMessages.TechnicalError) : GetLocalizedErrorMessage(error.IntErrorType);

            return new FileErrorDetailDto
            {
                ErrorMessage = errorMessage,
                RecordNumber = error.IntRowNo
            };
        }

        private string GetLocalizedStatus(SupplierVolumeFile file)
        {
            if (file.IntFileStatusId == (int)FileStatus.Uploaded)
            {
                return localizationService.GetString(StatusMessages.ProcessingLabel);
            }

            return currentUserService.LanguageId == Languages.English
                ? file.IntFileStatus?.StrName ?? string.Empty
                : file.IntFileStatus?.StrNameFr ?? string.Empty;
        }

        /// <summary>
        /// Extracts the original filename from blob filename format: {username}_{originalNameWithoutExtension}_{timestamp}{extension}
        /// </summary>
        /// <param name="blobFileName">The blob filename with username and timestamp prefix</param>
        /// <returns>The original filename without username and timestamp prefix</returns>
        private static string ExtractOriginalFileName(string blobFileName)
        {
            if (string.IsNullOrEmpty(blobFileName))
                return string.Empty;

            // Find first and last underscore positions
            var firstUnderscore = blobFileName.IndexOf('_', StringComparison.Ordinal);
            var lastUnderscore = blobFileName.LastIndexOf('_');

            // Need at least 2 underscores for expected format: {username}_{originalName}_{timestamp}{ext}
            if (firstUnderscore == -1 || lastUnderscore == -1 || firstUnderscore == lastUnderscore)
                return blobFileName; // Return as-is if format doesn't match

            // Extract original name between first and last underscore, then add extension
            var originalNameWithoutExtension = blobFileName[(firstUnderscore + 1)..lastUnderscore];
            var extension = Path.GetExtension(blobFileName);

            return originalNameWithoutExtension + extension;
        }

        private string GetLocalizedErrorMessage(Domain.Entities.SupplierVolumeErrorType errorType)
        {
            return currentUserService.LanguageId == Languages.English
                ? errorType.StrDescription
                : errorType.StrDescriptionFr;
        }

        public async Task<bool> CanUploadNewFileAsync(int supplierId)
        {
            // Validate supplier ownership for external users
            if (currentUserService.SupplierId.HasValue && currentUserService.SupplierId.Value != supplierId)
                throw new ForbiddenException(localizationService.GetString(ErrorMessages.SupplierAccessOnly));

            var processingMonth = DetermineProcessingMonth();
            var hasUpload = await HasCurrentMonthUploadAsync(supplierId, processingMonth);

            // Allow upload if no existing upload OR existing upload is in Upload Failed state
            if (!hasUpload) return true;

            var failedFile = await GetCurrentMonthFailedFileAsync(supplierId);
            return failedFile != null;
        }

        public async Task<FileRecordDto> GetFileRecordsAsync(int fileId, CancellationToken ct)
        {
            var languageId = currentUserService.LanguageId;

            var file = await (from f in context.GetDbSet<SupplierVolumeFile>().AsNoTracking()
                              join s in context.GetDbSet<Domain.Entities.Supplier>() on f.IntSupplierId equals s.IntSupplierId
                              join fs in context.GetDbSet<SupplierVolumeFileStatus>() on f.IntFileStatusId equals fs.IntFileStatusId
                              where f.IntDataFileId == fileId &&
                                    (!currentUserService.SupplierId.HasValue || f.IntSupplierId == currentUserService.SupplierId.Value)
                              select new
                              {
                                  SupplierName = s.StrName ?? string.Empty,
                                  Month = f.StrMonth ?? string.Empty,
                                  Year = f.StrYear ?? string.Empty,
                                  StatusId = f.IntFileStatusId,
                                  StatusName = languageId == Languages.English ? fs.StrName ?? string.Empty : fs.StrNameFr ?? string.Empty,
                              }).FirstOrDefaultAsync(ct);

            if (file == null)
                throw new NotFoundException($"File with id: {fileId} not found.");

            var displayStatus = file.StatusId == (int)FileStatus.Uploaded ? localizationService.GetString(StatusMessages.ProcessingLabel) : file.StatusName;

            var recordsQuery = BuildFileRecordsBaseQuery(fileId);

            // Apply default ordering by RecordNo for consistent results
            recordsQuery = recordsQuery.OrderBy(r => r.RecordNo);

            var records = await recordsQuery.ToListAsync(ct);

            return new FileRecordDto
            {
                SupplierName = file!.SupplierName,
                Month = file.Month,
                Year = file.Year,
                Status = displayStatus,
                Records = records
            };
        }


        public async Task ApproveFileAsync(int fileId, CancellationToken ct = default)
        {
            using var transaction = await context.Database.BeginTransactionAsync(ct);
            try
            {
                // Optimized: Single query for file approval validation
                var file = await context.GetDbSet<SupplierVolumeFile>()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(f => f.IntDataFileId == fileId, ct);

                if (file == null)
                    throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

                if (file.IntFileStatusId != (int)FileStatus.AramarkAudit)
                    throw new BadRequestException(localizationService.GetString(ErrorMessages.InvalidFileStatus));

                var hasUnMappedRecords = await context.GetDbSet<SupplierVolumeFileDatum>()
                    .AnyAsync(d => d.IntDataFileId == fileId &&
                   d.IntDataStatusId == (int)DataStatus.Unmapped, ct);

                if (hasUnMappedRecords)
                    throw new BadRequestException(localizationService.GetString(ValidationMessages.MappingRequired));

                await ExecuteUpdateFileStatusStoredProcedureAsync(fileId);

                await transaction.CommitAsync(ct);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }

        public async Task RejectFileAsync(int fileId, CancellationToken ct = default)
        {
            using var transaction = await context.Database.BeginTransactionAsync(ct);
            try
            {
                // Get file info before update for logging
                var file = await context.GetDbSet<SupplierVolumeFile>()
                    .FirstOrDefaultAsync(f => f.IntDataFileId == fileId, ct);

                if (file == null)
                    throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

                if (file.IntFileStatusId != (int)FileStatus.AramarkAudit)
                    throw new BadRequestException(localizationService.GetString(ErrorMessages.InvalidFileStatus));

                var oldStatus = file.IntFileStatusId;

                file.IntFileStatusId = (int)FileStatus.Rejected;

                // Log activity for file rejection
                LogActivity(
                    ActivityTypes.FileStatusUpdated,
                    fileId,
                    subjectId: null,
                    oldStatus.ToString(CultureInfo.InvariantCulture),
                    ((int)FileStatus.Rejected).ToString(CultureInfo.InvariantCulture),
                    nameof(RejectFileAsync));
                await context.SaveChangesAsync(ct);

                await transaction.CommitAsync(ct);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }

        public async Task DeleteFileAsync(int fileId, CancellationToken ct = default)
        {
            // Validate supplier ownership for external users first
            await ValidateSupplierOwnershipForReadOperationsAsync(fileId, ct);

            // Validate authorization second
            await ValidateOperationPermissionAsync(0, fileId, OperationType.Delete, ct);

            using var transaction = await context.Database.BeginTransactionAsync(ct);
            try
            {
                // Get file info before deletion for logging (optimized with AsNoTracking and projection)
                var fileInfo = await context.GetDbSet<SupplierVolumeFile>()
                    .AsNoTracking()
                    .Where(f => f.IntDataFileId == fileId)
                    .Select(f => new { f.IntDataFileId, f.StrFileName })
                    .FirstOrDefaultAsync(ct);

                if (fileInfo == null)
                    throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

                await context.GetDbSet<SupplierVolumeFileError>()
                    .Where(f => f.IntDataFileId == fileId)
                    .ExecuteDeleteAsync(ct);

                await context.GetDbSet<SupplierVolumeFileMonth>()
                    .Where(f => f.IntDataFileId == fileId)
                    .ExecuteDeleteAsync(ct);

                await context.GetDbSet<SupplierVolumeFileDatum>()
                    .Where(f => f.IntDataFileId == fileId)
                    .ExecuteDeleteAsync(ct);

                await context.GetDbSet<SupplierVolumeFile>()
                    .Where(f => f.IntDataFileId == fileId)
                    .ExecuteDeleteAsync(ct);

                // Log activity for file deletion
                LogActivity(
                    ActivityTypes.FileDeleted,
                    fileId,
                    null,
                    null,
                    null, // No new status as file is deleted
                    nameof(DeleteFileAsync));

                await context.SaveChangesAsync(ct);

                await transaction.CommitAsync(ct);

            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }

        public async Task<FileStatusResponse> GetFileStatusAsync(int fileId, CancellationToken ct = default)
        {
            // Optimized: Single query with join to get file info, record counts, and CanApprove flag
            var fileStatusData = await (from f in context.GetDbSet<SupplierVolumeFile>()
                                        join fs in context.GetDbSet<SupplierVolumeFileStatus>() on f.IntFileStatusId equals fs.IntFileStatusId into fsGroup
                                        from fs in fsGroup.DefaultIfEmpty()
                                        where f.IntDataFileId == fileId
                                        select new
                                        {
                                            File = f,
                                            StatusName = f.IntFileStatusId == (int)FileStatus.Uploaded ? localizationService.GetString(StatusMessages.ProcessingLabel) : (fs.StrName ?? string.Empty),
                                            TotalRecords = context.GetDbSet<SupplierVolumeFileDatum>()
                                                .Count(r => r.IntDataFileId == fileId),
                                            ProcessedRecords = context.GetDbSet<SupplierVolumeFileDatum>()
                                                .Count(r => r.IntDataFileId == fileId && r.IntDataStatusId == (int)DataStatus.ReadyToProcess),
                                            // CanApprove: File must be in Audit state AND have no unmapped records
                                            CanApprove = f.IntFileStatusId == (int)FileStatus.AramarkAudit &&
                                                        !context.GetDbSet<SupplierVolumeFileDatum>()
                                                            .Any(d => d.IntDataFileId == fileId && d.IntDataStatusId == (int)DataStatus.Unmapped)
                                        }).FirstOrDefaultAsync(ct);

            if (fileStatusData?.File == null)
            {
                return new FileStatusResponse
                {
                    FileId = fileId,
                    Status = localizationService.GetString(StatusMessages.NotFound),
                    StatusMessage = localizationService.GetString(StatusMessages.NotFound),
                    Progress = 0,
                    LastUpdated = DateTime.Now,
                    CanApprove = false
                };
            }

            var progress = fileStatusData.TotalRecords > 0
                ? (fileStatusData.ProcessedRecords * 100) / fileStatusData.TotalRecords
                : 0;

            return new FileStatusResponse
            {
                FileId = fileId,
                Status = fileStatusData.StatusName,
                StatusMessage = GetLocalizedStatusMessage(fileStatusData.StatusName),
                Progress = progress,
                LastUpdated = fileStatusData.File.DttmUploadDate ?? DateTime.Now,
                CanApprove = fileStatusData.CanApprove
            };
        }



        public async Task<RecordMappingDto> GetRecordMappingInfoAsync(int dataFileRecordId, CancellationToken ct)
        {
            var data = await (from svfd in context.GetDbSet<SupplierVolumeFileDatum>()
                              join cc in context.GetDbSet<Cpscustomer>()
                                  on svfd.StrAracpscustomerId equals cc.StrCustomerId into ccGroup
                              from cc in ccGroup.DefaultIfEmpty()
                              where svfd.IntDataFileRowId == dataFileRecordId
                              select new
                              {
                                  // Supplier data (always present)
                                  SupplierCustomerId = svfd.StrSupplierCustomerId ?? string.Empty,
                                  SupplierCustomerName = svfd.StrCustFileLocation ?? string.Empty,
                                  SupplierAddress = svfd.StrCustFileStaddress1 ?? string.Empty,
                                  SupplierCity = svfd.StrCustFileCity ?? string.Empty,
                                  SupplierProvince = svfd.StrCustFileProvinceCode ?? string.Empty,
                                  SupplierPostalCode = svfd.StrCustFilePostCode ?? string.Empty,

                                  // Aramark data (null when unmapped)
                                  AramarkCustomerDivisionClientId = cc.StrCassdivision != null && cc.StrClientNumber != null
                                      ? cc.StrCassdivision + "-" + cc.StrClientNumber
                                      : null,
                                  AramarkCustomerId = svfd.StrAracpscustomerId,
                                  AramarkCustomerName = cc.StrName,
                                  AramarkDivision = cc.StrCassdivision,
                                  AramarkAddress = cc.StrAddress1,
                                  AramarkCity = cc.StrCity,
                                  AramarkProvince = cc.StrStateProvCode,
                                  AramarkPostalCode = cc.StrZipcode,
                                  AramarkOpenDate = cc.OpenDate,
                                  AramarkCloseDate = cc.CloseDate
                              }).FirstOrDefaultAsync(ct);

            if (data == null)
                throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

            return MapToRecordMappingDto(data);
        }


        private async Task ValidateUploadEligibilityAsync(int supplierId, string processingMonth)
        {
            var hasUpload = await HasCurrentMonthUploadAsync(supplierId, processingMonth);
            if (!hasUpload) return;

            // Check if existing upload is in Upload Failed state - if so, delete it and allow new upload
            var failedFile = await GetCurrentMonthFailedFileAsync(supplierId);

            if (failedFile != null)
            {
                // Delete the failed file using existing method
                await DeleteFileAsync(failedFile.IntDataFileId);
                return;
            }

            throw new BadRequestException(localizationService.GetString(ValidationMessages.SameMonthUploadExists));
        }

        private async Task ProcessFileUploadAsync(IFormFile file, int supplierId, string processingMonth, int userId)
        {
            var (correlationId, blobFileName, blobName, metadata) = CreateBlobFileInfo(file, supplierId, processingMonth);

            var blobUrl = await blobService.UploadFileAsync(file.OpenReadStream(), blobName, _storageOptions.BlobContainerName, metadata);
            await SendFileProcessingMessageAsync(blobFileName, blobUrl, processingMonth, userId, supplierId, correlationId);
        }

        private (Guid CorrelationId, string BlobFileName, string BlobName, Dictionary<string, string> Metadata) CreateBlobFileInfo(IFormFile file, int supplierId, string processingMonth)
        {
            var correlationId = Guid.NewGuid();
            var currentTime = DateTime.Now;
            var timestamp = currentTime.ToString(FileNaming.FileNameTimestampFormat, CultureInfo.InvariantCulture);
            var username = currentUserService.Username;
            var originalNameWithoutExtension = Path.GetFileNameWithoutExtension(file.FileName);
            var extension = Path.GetExtension(file.FileName);
            var blobFileName = $"{username}_{originalNameWithoutExtension}_{timestamp}{extension}";
            // Upload files to root of blob container without supplier prefix
            var blobName = blobFileName;

            Dictionary<string, string> metadata = new(StringComparer.OrdinalIgnoreCase)
            {
                [BlobMetadata.MetadataOriginalFileName] = file.FileName,
                [BlobMetadata.MetadataProcessingMonth] = processingMonth,
                [BlobMetadata.MetadataSupplierId] = supplierId.ToString(CultureInfo.InvariantCulture),
                [BlobMetadata.MetadataUserId] = currentUserService.UserId.ToString(CultureInfo.InvariantCulture),
                [BlobMetadata.MetadataCorrelationId] = correlationId.ToString(),
                [BlobMetadata.MetadataUploadTimestamp] = currentTime.ToString("O", CultureInfo.InvariantCulture)
            };

            return (correlationId, blobFileName, blobName, metadata);
        }

        private async Task SendFileProcessingMessageAsync(string blobFileName, string blobUrl, string processingMonth, int userId, int supplierId, Guid correlationId)
        {
            var queueMessage = new FileUploadMessage
            {
                FileName = blobFileName,
                BlobPath = blobUrl,
                ContainerName = _storageOptions.BlobContainerName,
                ProcessingMonth = processingMonth,
                SupplierId = supplierId,
                UserSK = userId,
                CorrelationId = correlationId.ToString()
            };

            await queueService.SendMessageAsync(queueMessage, _storageOptions.QueueName);
        }

        private async Task<ICollection<BlobFileInfo>> GetBlobFilesAsync(int? supplierId, DateTime cutoffDate, CancellationToken ct)
        {
            // For internal users (supplierId is null), get all processing files
            // For external users, get files for their specific supplier
            return await blobService.GetProcessingFilesAsync(supplierId, processingMonth: null, cutoffDate: cutoffDate, cancellationToken: ct);
        }

        private async Task<List<DataFileDto>> MergeWithBlobFilesAsync(List<DataFileDto> dbFiles, ICollection<BlobFileInfo> blobFiles, CancellationToken ct)
        {
            var result = dbFiles;

            // Get blob-only files (not yet processed by SSIS)
            var blobOnlyFilesData = blobFiles.ToList();

            if (blobOnlyFilesData.Count > 0)
            {
                // Extract unique supplier IDs from blob files
                var supplierIds = blobOnlyFilesData
                    .Select(b => int.Parse(b.Metadata.GetValueOrDefault(BlobMetadata.MetadataSupplierId, "0"), CultureInfo.InvariantCulture))
                    .Where(id => id > 0)
                    .Distinct()
                    .ToList();

                // Fetch supplier names in a single query
                var supplierNames = await context.GetDbSet<Domain.Entities.Supplier>()
                    .Where(s => supplierIds.Contains(s.IntSupplierId))
                    .ToDictionaryAsync(s => s.IntSupplierId, s => s.StrName ?? string.Empty, ct);

                // Create blob file DTOs
                var blobOnlyFiles = blobOnlyFilesData.Select(b =>
                {
                    var supplierId = int.Parse(b.Metadata.GetValueOrDefault(BlobMetadata.MetadataSupplierId, "0"), CultureInfo.InvariantCulture);
                    // Get actual supplier name from database, fallback to empty string if not found
                    var supplierName = supplierNames.GetValueOrDefault(supplierId, string.Empty);

                    return new DataFileDto
                    {
                        FileId = 0, // No SSIS ID yet
                        SupplierName = supplierName,
                        Status = localizationService.GetString(StatusMessages.ProcessingLabel),
                        TotalVolume = 0,
                        TotalRecords = int.Parse(b.Metadata.GetValueOrDefault(BlobMetadata.MetadataTotalRecords, "0"), CultureInfo.InvariantCulture),
                        UnmappedRecords = 0,
                        Months = [], // Transaction months determined after SSIS processing
                        UploadDate = ConvertUtcToEst(b.LastModified)
                    };
                });

                result.AddRange(blobOnlyFiles);
            }

            return result;
        }

        private static string DetermineProcessingMonth()
        {
            // Business rule: Processing month is always previous month (YYYYMM format)
            var previousMonth = DateTime.Now.AddMonths(-1);
            return previousMonth.ToString(FileNaming.ProcessingMonthFormat, CultureInfo.InvariantCulture);
        }

        private int? DetermineEffectiveSupplierId(int? requestSupplierId)
        {
            // For external users: Use supplier ID from JWT claims (currentUserService.SupplierId)
            // For internal users: Use supplier ID provided in request (requestSupplierId)

            var userSupplierId = currentUserService.SupplierId;

            if (userSupplierId.HasValue)
            {
                // External user - must use their supplier ID from claims
                return userSupplierId.Value;
            }

            // Internal user - use provided supplier ID
            return requestSupplierId;
        }

        private async Task<int?> DetermineEffectiveUserIdAsync(int? requestSupplierId)
        {
            if (currentUserService.SupplierId.HasValue)
            {
                return currentUserService.UserId;
            }

            if (requestSupplierId.HasValue)
            {
                var userId = await context.GetDbSet<VwSupplierUser>()
                    .Where(u => u.IntSupplierId == requestSupplierId.Value && u.IntStatusId == (int)Status.Active)
                    .Select(u => u.IntUserSk)
                    .FirstOrDefaultAsync();

                return userId > 0 ? userId : null;
            }

            return null;
        }

        #region Query Builders
        private async Task<List<DataFileDto>> GetDataFilesFromDatabaseAsync(int? effectiveSupplierId, DateTime cutoffDate, CancellationToken ct)
        {
            var languageId = currentUserService.LanguageId;

            // First, get all file data with joins and CanApprove calculation
            var filesQuery = from f in context.GetDbSet<SupplierVolumeFile>().AsNoTracking()
                             join s in context.GetDbSet<Domain.Entities.Supplier>() on f.IntSupplierId equals s.IntSupplierId
                             join fs in context.GetDbSet<SupplierVolumeFileStatus>() on f.IntFileStatusId equals fs.IntFileStatusId
                             where (!effectiveSupplierId.HasValue || f.IntSupplierId == effectiveSupplierId) &&
                                   (f.DttmUploadDate == null || f.DttmUploadDate >= cutoffDate)
                             orderby f.DttmUploadDate descending
                             select new
                             {
                                 FileId = f.IntDataFileId,
                                 SupplierName = s.StrName ?? string.Empty,
                                 Month = f.StrMonth ?? string.Empty,
                                 Year = f.StrYear ?? string.Empty,
                                 TotalVolume = f.DecTotalVolume,
                                 TotalRecords = f.IntTotalRecords,
                                 UnmappedRecords = 0, // Will be populated separately
                                 StatusId = f.IntFileStatusId,
                                 StatusName = languageId == Languages.English ? fs.StrName ?? string.Empty : fs.StrNameFr ?? string.Empty,
                                 HasMultipleMonths = f.BlnHasMultipleMonths == 1,
                                 UploadDate = f.DttmUploadDate ?? DateTime.MinValue,
                                 // CanApprove: File must be in Audit state AND have no unmapped records
                                 CanApprove = f.IntFileStatusId == (int)FileStatus.AramarkAudit &&
                                             !context.GetDbSet<SupplierVolumeFileDatum>()
                                                 .Any(d => d.IntDataFileId == f.IntDataFileId && d.IntDataStatusId == (int)DataStatus.Unmapped)
                             };

            var rawFilesData = await filesQuery.ToListAsync(ct);

            // Convert to DataFileQueryResult with status mapping
            var filesData = ConvertToDataFileQueryResults(rawFilesData);

            if (filesData.Count > 0)
            {
                // Get all file IDs
                var fileIds = filesData.Select(f => f.FileId).ToList();

                // Single query to get unmapped counts for all files
                var unmappedCounts = await context.GetDbSet<SupplierVolumeFileDatum>()
                    .Where(d => fileIds.Contains(d.IntDataFileId) &&
                                DataStatusRules.UnmappedStatuses.Contains((DataStatus)d.IntDataStatusId))
                    .GroupBy(d => d.IntDataFileId)
                    .Select(g => new { FileId = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.FileId, x => x.Count, ct);

                // Update the unmapped counts in memory
                foreach (var file in filesData)
                {
                    file.UnmappedRecords = unmappedCounts.GetValueOrDefault(file.FileId, 0);
                }
            }

            var monthsData = await GetFileMonthsDataAsync(filesData, ct);
            var transformedData = TransformToDataFileDtos(filesData, monthsData);

            return transformedData;
        }

        private async Task<Dictionary<int, List<FileMonthData>>> GetFileMonthsDataAsync(List<DataFileQueryResult> filesData, CancellationToken ct)
        {
            var fileIds = filesData.Where(f => f.HasMultipleMonths).Select(f => f.FileId).ToArray();
            var monthsData = fileIds.Length > 0
                ? await context.GetDbSet<SupplierVolumeFileMonth>()
                    .AsNoTracking()
                    .Where(fm => fileIds.Contains(fm.IntDataFileId))
                    .Select(fm => new FileMonthData { IntDataFileId = fm.IntDataFileId, StrMonth = fm.StrMonth, StrYear = fm.StrYear })
                    .ToListAsync(ct)
                : [];

            return monthsData
                .GroupBy(m => m.IntDataFileId)
                .ToDictionary(g => g.Key, g => g.ToList());
        }
        private static List<DataFileDto> TransformToDataFileDtos(List<DataFileQueryResult> filesData, Dictionary<int, List<FileMonthData>> monthsByFileId)
        {
            return filesData.Select(item => new DataFileDto
            {
                FileId = item.FileId,
                SupplierName = item.SupplierName,
                TotalVolume = item.TotalVolume,
                TotalRecords = item.TotalRecords,
                UnmappedRecords = item.UnmappedRecords,
                Status = item.Status,
                HasMultipleMonths = item.HasMultipleMonths,
                UploadDate = item.UploadDate,
                CanApprove = item.CanApprove,
                Months = monthsByFileId.GetValueOrDefault(item.FileId, [])
                    .Select(m => new FileMonthInfo
                    {
                        Month = m.StrMonth,
                        Year = m.StrYear
                    }).ToList()
            }).ToList();
        }
        private IQueryable<RecordDto> BuildFileRecordsBaseQuery(int fileId)
        {
            var languageId = currentUserService.LanguageId;

            return from r in context.GetDbSet<SupplierVolumeFileDatum>().AsNoTracking()
                   join ds in context.GetDbSet<SupplierVolumeDataStatus>() on r.IntDataStatusId equals ds.IntDataStatusId
                   join cc in context.GetDbSet<Cpscustomer>() on r.StrAracpscustomerId equals cc.StrCustomerId into ccGroup
                   from cc in ccGroup.DefaultIfEmpty()
                   where r.IntDataFileId == fileId
                   select new RecordDto
                   {
                       RecordId = r.IntDataFileRowId,
                       DataFileId = r.IntDataFileId,
                       RecordNo = r.IntRecordNo,
                       SupplierCustomerId = r.StrSupplierCustomerId ?? string.Empty,
                       TransactionMonth = r.DttmTransactionMonth.HasValue ? r.DttmTransactionMonth.Value.ToString("MMM yyyy", CultureInfo.InvariantCulture) : string.Empty,
                       TotalVolume = r.DecTotalVolume,
                       Status = languageId == Languages.English ? ds.StrDataStatusName ?? string.Empty : ds.StrDataStatusNameFr ?? string.Empty,
                       AramarkCustomerId = cc.StrCassdivision != null && cc.StrClientNumber != null
                           ? cc.StrCassdivision + "-" + cc.StrClientNumber
                           : string.Empty
                   };
        }

        #endregion

        private string GetLocalizedStatusMessage(string status)
        {
            var resourceKey = StatusMessages.GetStatusMessage(status);
            return localizationService.GetString(resourceKey);
        }



        private static string GetCompanyNameByDivision(string? division)
        {
            return division switch
            {
                "09030" => CompanyConstants.CPS,
                "08300" => CompanyConstants.CpsGespraQuasep,
                "08130" or "08131" => CompanyConstants.Aramark,
                _ => CompanyConstants.GespraQuasep
            };
        }

        private List<DataFileQueryResult> ConvertToDataFileQueryResults(IEnumerable<dynamic> rawData)
        {
            return rawData.Select(f => new DataFileQueryResult
            {
                FileId = f.FileId,
                SupplierName = f.SupplierName,
                Month = f.Month,
                Year = f.Year,
                TotalVolume = f.TotalVolume,
                TotalRecords = f.TotalRecords,
                UnmappedRecords = f.UnmappedRecords,
                Status = f.StatusId == (int)FileStatus.Uploaded ? localizationService.GetString(StatusMessages.ProcessingLabel) : f.StatusName,
                HasMultipleMonths = f.HasMultipleMonths,
                UploadDate = f.UploadDate,
                CanApprove = f.CanApprove
            }).ToList();
        }

        private static DateTime ConvertUtcToEst(DateTimeOffset utcDateTime)
        {
            // Convert UTC to Eastern Time (EST/EDT)
            var easternTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
            return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime.UtcDateTime, easternTimeZone);
        }

        private async Task<bool> HasCurrentMonthUploadAsync(int supplierId, string processingMonth)
        {
            var currentDate = DateTime.UtcNow;

            var hasDbUpload = await context.GetDbSet<SupplierVolumeFile>()
                .AnyAsync(f =>
                    f.IntSupplierId == supplierId &&
                    f.IntFileStatusId != (int)FileStatus.UploadFailed &&
                    f.DttmUploadDate.HasValue &&
                    f.DttmUploadDate.Value.Year == currentDate.Year &&
                    f.DttmUploadDate.Value.Month == currentDate.Month);

            if (hasDbUpload)
                return true;

            var hasBlobUpload = await blobService.HasExistingUploadAsync(supplierId, processingMonth);
            return hasBlobUpload;
        }

        private async Task<SupplierVolumeFile?> GetCurrentMonthFailedFileAsync(int supplierId)
        {
            var currentDate = DateTime.UtcNow;

            return await context.GetDbSet<SupplierVolumeFile>()
                .FirstOrDefaultAsync(f => f.IntSupplierId == supplierId &&
                                         f.IntFileStatusId == (int)FileStatus.UploadFailed &&
                                         f.DttmUploadDate.HasValue &&
                                         f.DttmUploadDate.Value.Year == currentDate.Year &&
                                         f.DttmUploadDate.Value.Month == currentDate.Month);
        }

        public async Task MapRecordAsync(int recordId, MapRecordCommand command, CancellationToken ct = default)
        {
            // Validate supplier ownership for external users first
            await ValidateRecordAccessAsync(recordId, ct);

            // Validate authorization second
            await ValidateOperationPermissionAsync(recordId, 0, OperationType.Map, ct);

            using var transaction = await context.Database.BeginTransactionAsync(ct);
            try
            {
                // Validate record exists and get fileId for logging
                var record = await GetAndValidateRecordAsync(recordId, ct);
                var fileId = record.IntDataFileId;

                // Validate AramarkCustomerId exists if provided
                if (!string.IsNullOrEmpty(command.AramarkCustomerId))
                {
                    var customerExists = await context.GetDbSet<Cpscustomer>()
                        .AnyAsync(c => c.StrCustomerId == command.AramarkCustomerId, ct);

                    if (!customerExists)
                        throw new NotFoundException(string.Format(CultureInfo.InvariantCulture,
                            localizationService.GetString(ValidationMessages.CustomerNotFound), command.AramarkCustomerId));
                }

                var oldStatus = record.IntDataStatusId;
                var oldCustomerId = record.StrAracpscustomerId;

                // Update the mapping
                record.StrAracpscustomerId = command.AramarkCustomerId;

                // Determine new status based on stored procedure logic
                var newStatus = DetermineNewStatusForMapping(oldStatus, command.AramarkCustomerId);
                record.IntDataStatusId = newStatus;

                // Log activity for mapping change
                LogActivity(ActivityTypes.MappingUpdated, fileId, recordId, oldCustomerId, command.AramarkCustomerId, ActivityDetails.UpdateAramarkCustomerID);

                // Log activity for status change
                LogStatusChange(fileId, recordId, oldStatus, newStatus, ActivityDetails.UpdateAramarkCustomerIDStatus);

                await context.SaveChangesAsync(ct);
                await transaction.CommitAsync(ct);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }

        public async Task UnmapRecordAsync(int recordId, CancellationToken ct = default)
        {
            // Validate supplier ownership for external users first
            await ValidateRecordAccessAsync(recordId, ct);

            // Validate authorization second
            await ValidateOperationPermissionAsync(recordId, 0, OperationType.Unmap, ct);

            using var transaction = await context.Database.BeginTransactionAsync(ct);
            try
            {
                // Validate record exists and get fileId for logging
                var record = await GetAndValidateRecordAsync(recordId, ct);
                var fileId = record.IntDataFileId;

                var oldStatus = record.IntDataStatusId;
                var oldCustomerId = record.StrAracpscustomerId;

                // Unmap the record
                record.StrAracpscustomerId = null;

                // When unmapping, status should always be Unmapped
                var newStatus = (int)DataStatus.Unmapped;
                record.IntDataStatusId = newStatus;

                // Log activity for mapping change
                LogActivity(ActivityTypes.MappingUpdated, fileId, recordId, oldCustomerId, null, ActivityDetails.UpdateAramarkCustomerID);

                // Log activity for status change
                LogStatusChange(fileId, recordId, oldStatus, newStatus, ActivityDetails.UpdateAramarkCustomerIDStatus);

                await context.SaveChangesAsync(ct);
                await transaction.CommitAsync(ct);

            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }

        public async Task MarkRecordAsInvalidAsync(int recordId, CancellationToken ct = default)
        {
            // Validate authorization first
            await ValidateOperationPermissionAsync(recordId, 0, OperationType.MarkInvalid, ct);

            using var transaction = await context.Database.BeginTransactionAsync(ct);
            try
            {
                // Get the record with current status and fileId for logging
                var record = await GetAndValidateRecordAsync(recordId, ct);
                var fileId = record.IntDataFileId;

                // Validate record is in a valid state to be marked invalid
                if (record.IntDataStatusId != (int)DataStatus.NewMapping &&
                    record.IntDataStatusId != (int)DataStatus.Mapped &&
                    record.IntDataStatusId != (int)DataStatus.ModifiedMapping &&
                    record.IntDataStatusId != (int)DataStatus.Unmapped)
                {
                    throw new BadRequestException(localizationService.GetString(ValidationMessages.InvalidRecordStatus));
                }

                var oldStatus = record.IntDataStatusId;

                // Update status to Invalid
                record.IntDataStatusId = (int)DataStatus.InvalidRecord;

                // Log activity
                LogStatusChange(fileId, recordId, oldStatus, (int)DataStatus.InvalidRecord, ActivityDetails.UpdateAramarkCustomerIDStatus);
                await context.SaveChangesAsync(ct);
                await transaction.CommitAsync(ct);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }

        public async Task MarkRecordAsValidAsync(int recordId, CancellationToken ct = default)
        {
            // Validate authorization first
            await ValidateOperationPermissionAsync(recordId, 0, OperationType.MarkValid, ct);

            using var transaction = await context.Database.BeginTransactionAsync(ct);
            try
            {
                // Get the record with current status and fileId for logging
                var record = await GetAndValidateRecordAsync(recordId, ct);
                var fileId = record.IntDataFileId;

                // Validate record is in invalid state
                if (record.IntDataStatusId != (int)DataStatus.InvalidRecord)
                {
                    throw new BadRequestException(localizationService.GetString(ValidationMessages.OnlyInvalidRecordsCanBeMarkedValid));
                }

                var oldStatus = record.IntDataStatusId;

                // Get the previous status from activity tracking history to restore the correct status
                var previousStatusBeforeInvalid = await GetPreviousStatusFromActivityAsync(fileId, recordId, ct);

                // Determine the appropriate valid status - restore to what it was before being marked invalid
                int newStatus;
                if (previousStatusBeforeInvalid.HasValue)
                {
                    // Restore to the previous status from history
                    newStatus = previousStatusBeforeInvalid.Value;
                }
                else
                {
                    // Fallback: determine based on whether it has a mapping
                    newStatus = string.IsNullOrEmpty(record.StrAracpscustomerId)
                        ? (int)DataStatus.Unmapped
                        : (int)DataStatus.Mapped;
                }

                // Update status
                record.IntDataStatusId = newStatus;

                // Log activity
                LogStatusChange(fileId, recordId, oldStatus, newStatus, ActivityDetails.UpdateAramarkCustomerIDStatus);
                await context.SaveChangesAsync(ct);
                await transaction.CommitAsync(ct);
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }

        public async Task CompleteFileAsync(int fileId, CancellationToken ct = default)
        {
            // Validate authorization first
            await ValidateOperationPermissionAsync(0, fileId, OperationType.Complete, ct);

            using var transaction = await context.Database.BeginTransactionAsync(ct);
            try
            {
                // Validate file exists
                var file = await context.GetDbSet<SupplierVolumeFile>()
                    .FirstOrDefaultAsync(f => f.IntDataFileId == fileId, ct);

                if (file == null)
                    throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

                var validStatuses = new[] {
                    (int)FileStatus.ExternalMapping,
                    (int)FileStatus.AramarkReview,
                    (int)FileStatus.Rejected
                };

                if (!validStatuses.Contains(file.IntFileStatusId))
                    throw new BadRequestException(localizationService.GetString(ErrorMessages.InvalidFileStatus));

                // Validate all records are mapped or marked invalid - ONLY for AramarkReview status
                // ExternalMapping: External users can submit with unmapped records for Aramark to review
                // AramarkReview: Analysts must ensure all records are mapped/invalid before completing
                // AramarkAudit: Auditors use Approve/Reject operations, not Complete
                if (file.IntFileStatusId == (int)FileStatus.AramarkReview)
                {
                    var hasUnMappedRecords = await context.GetDbSet<SupplierVolumeFileDatum>()
                        .AnyAsync(d => d.IntDataFileId == fileId &&
                       d.IntDataStatusId == (int)DataStatus.Unmapped, ct);

                    if (hasUnMappedRecords)
                        throw new BadRequestException(localizationService.GetString(ValidationMessages.CompleteFileRequiresMappedRecords));
                }

                await ExecuteUpdateFileStatusStoredProcedureAsync(fileId);

                await transaction.CommitAsync(ct);

            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }


        private void LogActivity(int activityTypeId, int? fileId, int? subjectId, string? oldValue, string? newValue, string details)
        {
            var activity = new ActivityTracking
            {
                IntUserSk = currentUserService.UserId,
                IntActivityTypeId = activityTypeId,
                IntDataFileId = fileId,
                IntSubjectId = subjectId,
                DttmModified = DateTime.Now,
                StrOldValue = oldValue,
                StrNewValue = newValue,
                StrDetails = details
            };

            context.GetDbSet<ActivityTracking>().Add(activity);
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Performance", "CA1822:Mark members as static", Justification = "Method calls non-static LogActivity method")]
        private void LogStatusChange(int fileId, int subjectId, int oldStatus, int newStatus, string details)
        {
            LogActivity(
                ActivityTypes.DataStatusUpdated,
                fileId,
                subjectId,
                oldStatus.ToString(CultureInfo.InvariantCulture),
                newStatus.ToString(CultureInfo.InvariantCulture),
                details);
        }

        private static int DetermineNewStatusForMapping(int oldStatus, string? aramarkCustomerId)
        {
            // If unmapping (clearing the customer ID)
            if (string.IsNullOrEmpty(aramarkCustomerId))
            {
                return (int)DataStatus.Unmapped;
            }

            // If mapping with a customer ID, determine status based on previous status
            return oldStatus switch
            {
                (int)DataStatus.Unmapped => (int)DataStatus.NewMapping,
                (int)DataStatus.NewMapping => (int)DataStatus.NewMapping,
                (int)DataStatus.ModifiedMapping => (int)DataStatus.ModifiedMapping,
                (int)DataStatus.Mapped => (int)DataStatus.ModifiedMapping,
                (int)DataStatus.InvalidRecord => (int)DataStatus.Mapped, // When status = 8 then 9
                _ => (int)DataStatus.Mapped // Default case (else 9)
            };
        }

        private async Task<int?> GetPreviousStatusFromActivityAsync(int fileId, int recordId, CancellationToken ct)
        {
            // Query the last status change for this record
            var lastActivity = await context.GetDbSet<ActivityTracking>()
                .Where(a => a.IntDataFileId == fileId
                            && a.IntSubjectId == recordId
                            && a.IntActivityTypeId == ActivityTypes.DataStatusUpdated
                            && a.StrDetails == ActivityDetails.UpdateAramarkCustomerIDStatus)
                .OrderByDescending(a => a.DttmModified)
                .FirstOrDefaultAsync(ct);

            // Return the old status from the last activity if found
            return lastActivity != null && int.TryParse(lastActivity.StrOldValue, CultureInfo.InvariantCulture, out var oldStatus)
                ? oldStatus
                : (int?)null;
        }

        private async Task<SupplierVolumeFileDatum> GetAndValidateRecordAsync(int recordId, CancellationToken ct)
        {
            var record = await context.GetDbSet<SupplierVolumeFileDatum>()
                .FirstOrDefaultAsync(r => r.IntDataFileRowId == recordId, ct);

            if (record == null)
                throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

            return record;
        }

        private async Task ExecuteUpdateFileStatusStoredProcedureAsync(int fileId)
        {
            var fileIdParam = new SqlParameter("@intDataFileID", fileId);
            var userIdParam = new SqlParameter("@intUserSk", currentUserService.UserId);

            await context.Database.ExecuteSqlRawAsync(
                "EXEC [SPL].[SPL_UpdateFileStatus] @intDataFileID, @intUserSk",
                fileIdParam, userIdParam);
        }

        public async Task<IEnumerable<CustomerMatchResultDto>> GetPotentialCustomerMatchesAsync(
            int recordId,
            CancellationToken ct = default)
        {
            var data = await GetRecordMappingInfoAsync(recordId, ct);
            var province = data.SupplierInfo.Province;
            var supplierCustomerName = data.SupplierInfo.CustomerName;
            var supplierAddress = data.SupplierInfo.Address;

            var customers = await GetFilteredCustomersAsync(province, ct);
            return CalculateFuzzyMatchScores(customers, supplierCustomerName, supplierAddress);
        }

        private async Task<List<CustomerInfoDto>> GetFilteredCustomersAsync(
            string province,
            CancellationToken ct)
        {
            // Get customers by province with complete division filtering from legacy SQL
            return await context.GetDbSet<Cpscustomer>().AsNoTracking()
                .Where(c => c.StrStateProvCode == province &&
                           c.StrCassdivision != null &&
                           // Keep all company/division rules from legacy SQL 
                           ((c.StrCassdivision == "09030" || c.StrCassdivision == "08300") || // From company 1
                            c.StrCassdivision == "08130" || c.StrCassdivision == "08131" || // From company 3
                                                                                            // From company 2 (all other divisions except the ones explicitly listed)
                            (c.StrCassdivision != "09030" &&
                             c.StrCassdivision != "08130" &&
                             c.StrCassdivision != "08131")))
                .Select(c => new CustomerInfoDto
                {
                    CustomerId = c.StrCustomerId,
                    CustomerDivisionClientId = c.StrCassdivision != null && c.StrClientNumber != null
                        ? c.StrCassdivision + "-" + c.StrClientNumber
                        : c.StrCustomerId ?? string.Empty,
                    CustomerName = c.StrName ?? string.Empty,
                    Address = c.StrAddress1 ?? string.Empty,
                    City = c.StrCity ?? string.Empty,
                    Province = c.StrStateProvCode ?? string.Empty,
                    PostalCode = c.StrZipcode ?? string.Empty,
                    Company = GetCompanyNameByDivision(c.StrCassdivision),
                    OpenDate = c.OpenDate,
                    ClosedDate = c.CloseDate
                })
                .ToListAsync(ct);
        }

        private static IEnumerable<CustomerMatchResultDto> CalculateFuzzyMatchScores(
            List<CustomerInfoDto> customers,
            string? supplierCustomerName,
            string? supplierAddress)
        {
            var normalizedSupplierName = supplierCustomerName?.ToUpperInvariant().Trim();
            var normalizedSupplierAddress = supplierAddress?.ToUpperInvariant().Trim();

            return customers
                .Select(c =>
                {
                    var nameScore = !string.IsNullOrWhiteSpace(normalizedSupplierName)
                        ? Fuzz.PartialRatio(normalizedSupplierName, c.CustomerName.ToUpperInvariant())
                        : 0.0;

                    var addressScore = !string.IsNullOrWhiteSpace(normalizedSupplierAddress)
                        ? Fuzz.PartialRatio(normalizedSupplierAddress, c.Address.ToUpperInvariant())
                        : 0.0;

                    return new CustomerMatchResultDto
                    {
                        CustomerId = c.CustomerId,
                        CustomerDivisionClientId = c.CustomerDivisionClientId,
                        CustomerName = c.CustomerName,
                        Address = c.Address,
                        City = c.City,
                        Province = c.Province,
                        PostalCode = c.PostalCode,
                        Company = c.Company,
                        OpenDate = c.OpenDate,
                        ClosedDate = c.ClosedDate,
                        CustomerNameScore = nameScore,
                        AddressScore = addressScore
                    };
                })
                .OrderByDescending(c => c.CustomerNameScore)
                .ThenByDescending(c => c.AddressScore);
        }

        #region Authorization Helpers

        private async Task ValidateSupplierOwnershipForReadOperationsAsync(int fileId, CancellationToken ct)
        {
            // Only validate for external users (those with SupplierId)
            if (currentUserService.SupplierId.HasValue)
            {
                var fileSupplier = await context.GetDbSet<SupplierVolumeFile>()
                    .AsNoTracking()
                    .Where(f => f.IntDataFileId == fileId)
                    .Select(f => f.IntSupplierId)
                    .FirstOrDefaultAsync(ct);

                if (fileSupplier == 0)
                    throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

                if (fileSupplier != currentUserService.SupplierId.Value)
                    throw new ForbiddenException(localizationService.GetString(ErrorMessages.SupplierAccessOnly));
            }
            // Internal users (SupplierId == null) can access all suppliers' files
        }

        private async Task ValidateRecordAccessAsync(int dataFileRecordId, CancellationToken ct)
        {
            // Get fileId for ownership validation
            var fileId = await context.GetDbSet<SupplierVolumeFileDatum>()
                .AsNoTracking()
                .Where(r => r.IntDataFileRowId == dataFileRecordId)
                .Select(r => r.IntDataFileId)
                .FirstOrDefaultAsync(ct);

            if (fileId == 0)
                throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

            // Validate supplier ownership for external users
            await ValidateSupplierOwnershipForReadOperationsAsync(fileId, ct);
        }

        private static RecordMappingDto MapToRecordMappingDto(dynamic data)
        {
            return new RecordMappingDto
            {
                SupplierInfo = new CustomerInfoDto
                {
                    CustomerId = data.SupplierCustomerId,
                    CustomerName = data.SupplierCustomerName,
                    Address = data.SupplierAddress,
                    City = data.SupplierCity,
                    Province = data.SupplierProvince,
                    PostalCode = data.SupplierPostalCode
                },
                AramarkInfo = data.AramarkCustomerId != null ? new CustomerInfoDto
                {
                    CustomerId = data.AramarkCustomerId,
                    CustomerDivisionClientId = data.AramarkCustomerDivisionClientId,
                    CustomerName = data.AramarkCustomerName ?? string.Empty,
                    Address = data.AramarkAddress ?? string.Empty,
                    City = data.AramarkCity ?? string.Empty,
                    Province = data.AramarkProvince ?? string.Empty,
                    PostalCode = data.AramarkPostalCode ?? string.Empty,
                    OpenDate = data.AramarkOpenDate,
                    ClosedDate = data.AramarkCloseDate
                } : null
            };
        }

        private async Task<int> GetFileStatusFromRecordAsync(int recordId, CancellationToken ct)
        {
            // Get the FILE status that contains this record
            var fileStatus = await context.GetDbSet<SupplierVolumeFileDatum>()
                .Where(r => r.IntDataFileRowId == recordId)
                .Join(context.GetDbSet<SupplierVolumeFile>(),
                      r => r.IntDataFileId,
                      f => f.IntDataFileId,
                      (r, f) => f.IntFileStatusId)  // FILE status, not record status
                .FirstOrDefaultAsync(ct);

            if (fileStatus == 0)
                throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

            return fileStatus;
        }

        private async Task<int> GetFileStatusFromFileAsync(int fileId, CancellationToken ct)
        {
            var fileStatus = await context.GetDbSet<SupplierVolumeFile>()
                .Where(f => f.IntDataFileId == fileId)
                .Select(f => f.IntFileStatusId)
                .FirstOrDefaultAsync(ct);

            if (fileStatus == 0)
                throw new NotFoundException(localizationService.GetString(ErrorMessages.ResourceNotFound));

            return fileStatus;
        }

        private async Task ValidateOperationPermissionAsync(int recordId, int fileId, string operationType, CancellationToken ct)
        {
            // Get FILE status (not record status)
            int fileStatus = recordId > 0
                ? await GetFileStatusFromRecordAsync(recordId, ct)
                : await GetFileStatusFromFileAsync(fileId, ct);

            // Map operation + file status to required permission
            string requiredPermission = GetRequiredPermission(operationType, fileStatus);

            if (!currentUserService.HasPermissionInModule(Modules.Supplier, requiredPermission))
                throw new ForbiddenException(localizationService.GetString(ErrorMessages.InsufficientPermissions));
        }

        private static string GetRequiredPermission(string operationType, int fileStatus)
        {
            return (operationType, fileStatus) switch
            {
                // Mapping operations based on FILE status
                (OperationType.Map or OperationType.Unmap, (int)FileStatus.ExternalMapping)
                    => Permissions.MapUnmapExternalMappingFiles,
                (OperationType.Map or OperationType.Unmap, (int)FileStatus.AramarkReview or (int)FileStatus.Rejected)
                    => Permissions.MapUnmapAramarkReviewOrRejectedFiles,
                (OperationType.Map or OperationType.Unmap, (int)FileStatus.AramarkAudit)
                    => Permissions.MapUnmapAramarkAuditFiles,

                // Record validation operations based on FILE status
                (OperationType.MarkValid or OperationType.MarkInvalid, (int)FileStatus.AramarkReview or (int)FileStatus.Rejected)
                    => Permissions.MarkAramarkReviewOrRejectedRecordValidInvalid,
                (OperationType.MarkValid or OperationType.MarkInvalid, (int)FileStatus.AramarkAudit)
                    => Permissions.MarkAramarkAuditRecordValidInvalid,

                // File operations
                (OperationType.Approve or OperationType.Reject, (int)FileStatus.AramarkAudit)
                    => Permissions.ApproveRejectAramarkAuditFiles,
                (OperationType.Complete, (int)FileStatus.ExternalMapping)
                    => Permissions.MapUnmapExternalMappingFiles,
                (OperationType.Complete, (int)FileStatus.AramarkReview or (int)FileStatus.Rejected)
                    => Permissions.MapUnmapAramarkReviewOrRejectedFiles,
                (OperationType.Delete, (int)FileStatus.ExternalMapping)
                    => Permissions.DeleteExternalMappingFiles,
                (OperationType.Delete, (int)FileStatus.AramarkReview or (int)FileStatus.Rejected)
                    => Permissions.DeleteAramarkReviewOrRejectedFiles,
                (OperationType.Delete, (int)FileStatus.AramarkAudit)
                    => Permissions.DeleteAramarkAuditFiles,
                (OperationType.Delete, (int)FileStatus.Approved)
                    => Permissions.DeleteApprovedFiles,
                (OperationType.Upload, _)
                    => Permissions.UploadFile,

                _ => throw new ForbiddenException($"Operation '{operationType}' not allowed for file status '{((SupplierVolumeErrorTypeEnum)fileStatus).ToString()}'")
            };
        }

        #endregion
    }
}