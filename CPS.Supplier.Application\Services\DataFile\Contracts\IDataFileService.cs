using CPS.Supplier.Application.Services.DataFile.Commands;
using CPS.Supplier.Application.Services.DataFile.Dto;

namespace CPS.Supplier.Application.Services.DataFile.Contracts
{
    public interface IDataFileService
    {
        // Data file operations
        Task UploadFileAsync(UploadFileCommand command);
        Task<IReadOnlyList<DataFileDto>> GetDataFilesAsync(CancellationToken ct);
        Task<FileRecordDto> GetFileRecordsAsync(int fileId, CancellationToken ct);
        Task<FileErrorSummaryDto> GetFileErrorSummaryAsync(int fileId, CancellationToken ct);
        Task<RecordMappingDto> GetRecordMappingInfoAsync(int dataFileRecordId, CancellationToken ct);

        // File management
        Task ApproveFileAsync(int fileId, CancellationToken ct = default);
        Task RejectFileAsync(int fileId, CancellationToken ct = default);
        Task DeleteFileAsync(int fileId, CancellationToken ct = default);
        Task<FileStatusResponse> GetFileStatusAsync(int fileId, CancellationToken ct = default);
        Task CompleteFileAsync(int fileId, CancellationToken ct = default);

        // Upload eligibility
        Task<bool> CanUploadNewFileAsync(int supplierId);

        // Record mapping operations
        Task MapRecordAsync(int recordId, MapRecordCommand command, CancellationToken ct = default);
        Task UnmapRecordAsync(int recordId, CancellationToken ct = default);
        Task MarkRecordAsInvalidAsync(int recordId, CancellationToken ct = default);
        Task MarkRecordAsValidAsync(int recordId, CancellationToken ct = default);

        // Customer matching
        Task<IEnumerable<CustomerMatchResultDto>> GetPotentialCustomerMatchesAsync(
            int recordId,
            CancellationToken ct = default);
    }
}