﻿namespace CPS.Supplier.Application.Services.UserContext.Contracts;
public interface ICurrentUserService
{
    int UserId { get; }
    string Username { get; }
    ICollection<string> Roles { get; }
    ICollection<string> Permissions { get; }
    ICollection<string> Modules { get; }
    IDictionary<string, List<string>> ModulePermissions { get; }
    int? SupplierId { get; }
    string? SupplierName { get; }
    int? FacilityId { get; }
    int LanguageId { get; }
    bool IsSuperAdmin { get; }

    bool HasRole(string roleName);
    bool HasPermission(string permissionName);
    bool HasModuleAccess(string moduleName);
    bool HasPermissionInModule(string moduleName, string permissionName);
    bool HasAnyRole(params string[] roleNames);
    bool HasAnyPermission(params string[] permissionNames);
    bool HasAnyPermissionInModule(string moduleName);
    IDictionary<string, string> GetCustomClaims(string prefix = "custom:");
}