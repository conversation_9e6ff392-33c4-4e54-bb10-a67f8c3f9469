﻿using System.Collections.ObjectModel;
using System.Globalization;
using System.Security.Claims;
using System.Text.Json;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.UserContext.Contracts;
using Microsoft.AspNetCore.Http;

namespace CPS.Supplier.Application.Services.UserContext;
public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CurrentUserService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    private ClaimsPrincipal? CurrentUser => _httpContextAccessor.HttpContext?.User;

    public int UserId
    {
        get
        {
            var userIdClaim = CurrentUser?.FindFirst(CustomClaimTypes.UserId)?.Value;

            if (int.TryParse(userIdClaim, NumberStyles.Integer, CultureInfo.InvariantCulture, out var userId))
            {
                return userId;
            }

            throw new InvalidOperationException("User ID claim is not present or invalid.");
        }
    }

    public string Username
    {
        get
        {
            var userName = CurrentUser?.FindFirst(CustomClaimTypes.Username)?.Value ?? string.Empty;

            if (!string.IsNullOrEmpty(userName))
            {
                return userName;
            }

            throw new InvalidOperationException("Username claim is not present or invalid.");
        }
    }

    public ICollection<string> Roles
    {
        get
        {
            if (CurrentUser == null) return new ReadOnlyCollection<string>(Array.Empty<string>());

            var roles = CurrentUser.FindAll(ClaimTypes.Role)
                .Select(c => c.Value)
                .Where(role => !string.IsNullOrEmpty(role))
                .ToList();
            return new ReadOnlyCollection<string>(roles);
        }
    }

    public ICollection<string> Permissions
    {
        get
        {
            if (CurrentUser == null) return new ReadOnlyCollection<string>(Array.Empty<string>());

            var permissions = CurrentUser.FindAll(CustomClaimTypes.Permission)
                .Select(c => c.Value)
                .Where(permission => !string.IsNullOrEmpty(permission))
                .ToList();
            return new ReadOnlyCollection<string>(permissions);
        }
    }

    public ICollection<string> Modules
    {
        get
        {
            if (CurrentUser == null) return new ReadOnlyCollection<string>(Array.Empty<string>());

            var modules = CurrentUser.FindAll(CustomClaimTypes.Module)
                .Select(c => c.Value)
                .Where(module => !string.IsNullOrEmpty(module))
                .ToList();
            return new ReadOnlyCollection<string>(modules);
        }
    }

    public IDictionary<string, List<string>> ModulePermissions
    {
        get
        {
            if (CurrentUser == null) return new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);

            var modulePermissions = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);
            var modulePermissionClaim = CurrentUser.FindFirst(CustomClaimTypes.ModulePermissions);

            if (modulePermissionClaim != null && !string.IsNullOrEmpty(modulePermissionClaim.Value))
            {
                var deserializedModulePermissions = JsonSerializer.Deserialize<Dictionary<string, List<string>>>(modulePermissionClaim.Value);

                if (deserializedModulePermissions != null)
                {
                    foreach (var kvp in deserializedModulePermissions)
                    {
                        if (!string.IsNullOrEmpty(kvp.Key) && kvp.Value?.Count > 0)
                        {
                            var validPermissions = kvp.Value
                                .Where(p => !string.IsNullOrEmpty(p))
                                .ToList();

                            if (validPermissions?.Count > 0)
                            {
                                modulePermissions[kvp.Key] = validPermissions;
                            }
                        }
                    }
                }
            }

            return modulePermissions;
        }
    }

    public int? SupplierId
    {
        get
        {
            var supplierIdClaim = CurrentUser?.FindFirst(CustomClaimTypes.SupplierId)?.Value;
            return int.TryParse(supplierIdClaim, NumberStyles.Integer, CultureInfo.InvariantCulture, out var supplierId) ? supplierId : null;
        }
    }

    public string? SupplierName
    {
        get
        {
            var supplierNameClaim = CurrentUser?.FindFirst(CustomClaimTypes.SupplierName)?.Value;
            return string.IsNullOrEmpty(supplierNameClaim) ? null : supplierNameClaim;
        }
    }

    public int? FacilityId
    {
        get
        {
            var facilityIdClaim = CurrentUser?.FindFirst(CustomClaimTypes.FacilityId)?.Value;
            return int.TryParse(facilityIdClaim, NumberStyles.Integer, CultureInfo.InvariantCulture, out var facilityId) ? facilityId : null;
        }
    }

    public int LanguageId
    {
        get
        {
            var languageIdClaim = CurrentUser?.FindFirst(CustomClaimTypes.LanguageId)?.Value;

            if (int.TryParse(languageIdClaim, NumberStyles.Integer, CultureInfo.InvariantCulture, out var languageId))
            {
                return languageId;
            }

            throw new InvalidOperationException("language ID claim is not present or invalid.");
        }
    }

    public bool IsSuperAdmin
    {
        get
        {
            var isSuperAdminClaim = CurrentUser?.FindFirst(CustomClaimTypes.IsSuperAdmin)?.Value;

            if (bool.TryParse(isSuperAdminClaim, out var isSuperAdmin))
            {
                return isSuperAdmin;
            }

            return false;
        }
    }

    public bool HasRole(string roleName)
    {
        return CurrentUser?.IsInRole(roleName) ?? false;
    }

    public bool HasPermission(string permissionName)
    {
        return ModulePermissions.Values.Any(permissions =>
            permissions.Any(p => string.Equals(p, permissionName, StringComparison.OrdinalIgnoreCase)));
    }

    public bool HasModuleAccess(string moduleName)
    {
        return CurrentUser?.FindAll(CustomClaimTypes.Module)
            .Any(c => string.Equals(c.Value, moduleName, StringComparison.OrdinalIgnoreCase)) ?? false;
    }

    public bool HasPermissionInModule(string moduleName, string permissionName)
    {
        return ModulePermissions.TryGetValue(moduleName, out var permissions) &&
               permissions.Any(p => string.Equals(p, permissionName, StringComparison.OrdinalIgnoreCase));
    }

    public bool HasAnyPermissionInModule(string moduleName)
    {
        return ModulePermissions.TryGetValue(moduleName, out var permissions) && permissions.Count != 0;
    }

    public bool HasAnyRole(params string[] roleNames)
    {
        return roleNames.Any(role => HasRole(role));
    }

    public bool HasAnyPermission(params string[] permissionNames)
    {
        return permissionNames.Any(permission => HasPermission(permission));
    }

    public IDictionary<string, string> GetCustomClaims(string prefix = "custom:")
    {
        if (CurrentUser == null) return new Dictionary<string, string>(StringComparer.Ordinal);

        return CurrentUser.Claims
            .Where(c => c.Type.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
            .ToDictionary(c => c.Type.Substring(prefix.Length), c => c.Value, StringComparer.Ordinal);
    }
}