﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierVolumeFileErrorConfiguration : IEntityTypeConfiguration<SupplierVolumeFileError>
    {
        public void Configure(EntityTypeBuilder<SupplierVolumeFileError> entity)
        {
            entity.HasKey(e => e.IntFileErrorId).IsClustered(false);

            entity.ToTable("SupplierVolumeFileErrors", "SPL");

            entity.Property(e => e.IntFileErrorId).HasColumnName("intFileErrorID");
            entity.Property(e => e.IntDataFileId).HasColumnName("intDataFileID");
            entity.Property(e => e.IntErrorTypeId).HasColumnName("intErrorTypeID");
            entity.Property(e => e.IntRowNo).HasColumnName("intRowNo");
            entity.Property(e => e.StrColumnName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("strColumnName");
            entity.Property(e => e.StrSqlerror)
                .HasMaxLength(5000)
                .IsUnicode(false)
                .HasColumnName("strSQLError");

            entity.HasOne(d => d.IntDataFile).WithMany(p => p.SupplierVolumeFileErrors)
                .HasForeignKey(d => d.IntDataFileId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SupplierVolumeFileErrors_SupplierVolumeFile");

            entity.HasOne(d => d.IntErrorType).WithMany(p => p.SupplierVolumeFileErrors)
                .HasForeignKey(d => d.IntErrorTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SupplierVolumeFileErrors_SupplierVolumeErrorTypes");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierVolumeFileError> entity);
    }
}
