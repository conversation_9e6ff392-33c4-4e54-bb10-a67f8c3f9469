{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"SupplierConnectionString": ""}, "ApplicationInsights": {"ConnectionString": ""}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "Auth0": {"Domain": "", "Audience": ""}, "AdministrationApi": {"BaseUrl": "", "Auth0": {"TokenUrl": "https://cpsdev.us.auth0.com/oauth/token", "ClientId": "", "ClientSecret": "", "M2MAudience": ""}, "MasterData": {"Language": "/api/MasterData/company-languages", "Status": "/api/MasterData/statuses", "AddressType": "/api/MasterData/address-types", "PhoneType": "/api/MasterData/phone-types", "Countries": "/api/MasterData/countries", "Provinces": "/api/MasterData/provinces", "Role": "/api/MasterData/roles", "CountryCodes": "/api/MasterData/country-codes"}, "User": {"CreateSupplierUser": "/api/user/create-supplier-user", "GetUserDetailId": "/api/User/{userId}/supplier-user-detail", "UpdateUser": "/api/user/{userId}/update-supplier-user", "UpdateSupplierUserStatus": "/api/user/{userId}/update-supplier-user-status", "GetPasswordPolicy": "/api/user/password-policy", "UsernameExists": "/api/user/username-exists", "EmailExists": "/api/user/email-exists"}, "Polly": {"RetryCount": 3, "BaseDelaySeconds": 1.0, "TimeoutSeconds": 30, "UseJitter": true}}, "KeyVault": {"Enabled": true, "Endpoint": "https://your-key-vault-name.vault.azure.net/"}, "Cors": {"AllowedOrigins": ""}, "EnabledApplicationInsightsLogger": "true", "EnabledApplicationInsightsMetrics": "true", "ShowDetailedExceptionMessages": true}