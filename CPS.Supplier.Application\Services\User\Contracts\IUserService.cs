﻿using CPS.Supplier.Application.Services.User.Commands;
using CPS.Supplier.Application.Services.User.Dto;
using CPS.Supplier.Application.Services.UserContext.Dto;

namespace CPS.Supplier.Application.Services.User.Contracts;

public interface IUserService
{
    Task<UserDetailDto?> GetUserByIdAsync(int userId, CancellationToken cancellationToken = default);
    Task<int> CreateUser(UpsertUserCommand command, CancellationToken cancellationToken = default);
    Task<bool> UpdateUser(int userId, UpsertUserCommand command, CancellationToken cancellationToken = default);
    Task UpdateUserStatusAsync(int userId, bool isActive, CancellationToken cancellationToken = default);
    UserContextDto GetCurrentUserContext();
    Task<bool> EmailExists(string email, CancellationToken cancellationToken = default);
    Task<bool> UsernameExists(string username, CancellationToken cancellationToken = default);
    Task<PasswordPolicyDto> GetPasswordPolicy(CancellationToken cancellationToken = default);
}