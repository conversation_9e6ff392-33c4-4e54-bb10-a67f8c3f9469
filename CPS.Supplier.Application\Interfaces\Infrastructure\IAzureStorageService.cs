using CPS.Supplier.Application.Services.DataFile.Dto;

namespace CPS.Supplier.Application.Interfaces.Infrastructure;

public interface IAzureStorageService
{
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string containerName, IReadOnlyDictionary<string, string> metadata, CancellationToken cancellationToken = default);
    Task<Stream> DownloadFileAsync(string fileName, string containerName, CancellationToken cancellationToken = default);
    Task<bool> DeleteFileAsync(string fileName, string containerName, CancellationToken cancellationToken = default);
    Task<ICollection<BlobFileInfo>> GetProcessingFilesAsync(int? supplierId, string? processingMonth = null, DateTime? cutoffDate = null, CancellationToken cancellationToken = default);
    Task<bool> HasExistingUploadAsync(int supplierId, string processingMonth, CancellationToken cancellationToken = default);
}