using System.Linq.Expressions;
using CPS.Supplier.Application.Services.Common.Dto;

namespace CPS.Supplier.Application.Services.Common.Extensions;

/// <summary>
/// Extension methods for IQueryable to support pagination, filtering, and sorting
/// </summary>
public static class QueryableExtensions
{
    /// <summary>
    /// Apply pagination to a query
    /// </summary>
    public static IQueryable<T> ApplyPaging<T>(this IQueryable<T> query, int pageNumber, int pageSize)
    {
        return query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
    }

    /// <summary>
    /// Convert a query to a paged response
    /// </summary>
    public static async Task<PagedResponseDto<T>> ToPagedResponseAsync<T>(
        this IQueryable<T> query,
        int pageNumber,
        int pageSize,
        CancellationToken ct = default)
    {
        var totalCount = await query.CountAsync(ct);
        var items = await query.ApplyPaging(pageNumber, pageSize).ToListAsync(ct);

        return new PagedResponseDto<T>
        {
            Items = items,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalCount = totalCount,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    /// <summary>
    /// Convert a query to a paged response based on a paged query object
    /// </summary>
    public static async Task<PagedResponseDto<T>> ToPagedResponseAsync<T>(
        this IQueryable<T> query,
        PagedBaseQuery pagedQuery,
        CancellationToken ct = default)
    {
        return await query.ToPagedResponseAsync(pagedQuery.PageNumber, pagedQuery.PageSize, ct);
    }

    /// <summary>
    /// Apply dynamic sorting to a query
    /// </summary>
    /// <param name="query">The query to sort</param>
    /// <param name="sortBy">Property name to sort by</param>
    /// <param name="isDescending">Whether to sort in descending order</param>
    /// <param name="sortExpressionMap">Dictionary mapping property names to sort expressions</param>
    public static IQueryable<T> ApplySorting<T>(
        this IQueryable<T> query,
        string sortBy,
        bool isDescending,
        IReadOnlyDictionary<string, Expression<Func<T, object>>> sortExpressionMap)
    {
        if (string.IsNullOrWhiteSpace(sortBy) || sortExpressionMap.Count == 0)
        {
            return query; // No sorting if no sort field or empty map
        }

        // Handle multi-field sorting (e.g., "LastName,FirstName")
        var sortFields = sortBy.Split(',', StringSplitOptions.RemoveEmptyEntries)
                              .Select(field => field.Trim())
                              .ToArray();

        if (sortFields.Length > 1)
        {
            // Multi-field sorting: apply first field, then subsequent fields
            IOrderedQueryable<T>? orderedQuery = null;

            foreach (var (field, index) in sortFields.Select((f, i) => (f, i)))
            {
                if (sortExpressionMap.TryGetValue(field, out var expression))
                {
                    if (index == 0)
                    {
                        // First field determines primary sort direction
                        orderedQuery = isDescending
                            ? query.OrderByDescending(expression)
                            : query.OrderBy(expression);
                    }
                    else
                    {
                        // Subsequent fields always use ascending for multi-field sorts
                        orderedQuery = orderedQuery!.ThenBy(expression);
                    }
                }
            }

            return orderedQuery ?? query;
        }

        // Standard single-field sorting
        if (sortExpressionMap.TryGetValue(sortBy, out var singleExpression))
        {
            return isDescending
                ? query.OrderByDescending(singleExpression)
                : query.OrderBy(singleExpression);
        }

        // If sort field not found, return the original query without sorting
        return query;
    }

    /// <summary>
    /// Apply dynamic sorting to a query based on a paged query object
    /// </summary>
    public static IQueryable<T> ApplySorting<T>(
        this IQueryable<T> query,
        PagedBaseQuery pagedQuery,
        IReadOnlyDictionary<string, Expression<Func<T, object>>> sortExpressionMap)
    {
        return query.ApplySorting(pagedQuery.SortBy, pagedQuery.IsDescendingOrder, sortExpressionMap);
    }
}