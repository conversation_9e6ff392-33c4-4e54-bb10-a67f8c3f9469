namespace CPS.Supplier.Application.Common;

/// <summary>
/// Blob storage metadata keys.
/// Extracted from ProcessingConstants for Microsoft .NET 8 standards compliance.
/// </summary>
public static class BlobMetadata
{
    public const string MetadataOriginalFileName = "OriginalFileName";
    public const string MetadataProcessingMonth = "ProcessingMonth";
    public const string MetadataSupplierId = "SupplierId";
    public const string MetadataUserId = "UserId";
    public const string MetadataTotalRecords = "TotalRecords";
    public const string MetadataCorrelationId = "CorrelationId";
    public const string MetadataUploadTimestamp = "UploadTimestamp";
}