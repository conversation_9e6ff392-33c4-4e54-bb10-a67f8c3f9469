namespace CPS.Supplier.Application.Services.DataFile.Dto;

public class FileErrorDetailDto
{
    public string ErrorMessage { get; set; } = string.Empty;
    public int RecordNumber { get; set; }
}

public class FileErrorSummaryDto
{
    public string FileName { get; set; } = string.Empty;
    public string Month { get; set; } = string.Empty;
    public string Year { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public IEnumerable<FileErrorDetailDto> Errors { get; set; } = [];
}