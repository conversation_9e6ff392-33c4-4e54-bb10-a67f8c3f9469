namespace CPS.Supplier.Application.Common;

/// <summary>
/// Pre-built policy strings for common authorization scenarios.
/// In Supplier API, all users are supplier users.
/// Extracted from AuthorizationConstants for Microsoft .NET 8 standards compliance.
/// </summary>
public static class Policies
{
    // User Management Policies
    public const string CreateUser = $"{Modules.Supplier}.{Permissions.CreateUser}";
    public const string EditUser = $"{Modules.Supplier}.{Permissions.EditUser}";

    public const string ViewUserOrManage = $"{Modules.Supplier}.{Permissions.ViewUser}," +
                                           $"{Modules.Supplier}.{Permissions.CreateUser}," +
                                           $"{Modules.Supplier}.{Permissions.EditUser}";

    // File Operation Policies
    public const string UploadFile = $"{Modules.Supplier}.{Permissions.UploadFile}";
    public const string ApproveRejectAramarkAudit = $"{Modules.Supplier}.{Permissions.ApproveRejectAramarkAuditFiles}";
}