using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Resources;
using CPS.Supplier.Application.Services.Localization;
using Microsoft.Extensions.Localization;

namespace CPS.Supplier.UnitTests
{
    public class LocalizationServiceTests
    {
        private readonly IStringLocalizer<Messages> _stringLocalizer;
        private readonly LocalizationService _localizationService;

        public LocalizationServiceTests()
        {
            // Setup mock string localizer
            _stringLocalizer = Substitute.For<IStringLocalizer<Messages>>();

            // Create the actual service (not a mock)
            _localizationService = new LocalizationService(_stringLocalizer);
        }

        [Fact]
        public void GetStringWithValidKeyReturnsLocalizedMessage()
        {
            // Arrange
            var expectedMessage = "File size exceeds maximum allowed limit";
            var localizedString = new LocalizedString(ValidationMessages.InvalidFileSize, expectedMessage, false);
            _stringLocalizer[ValidationMessages.InvalidFileSize].Returns(localizedString);

            // Act
            var result = _localizationService.GetString(ValidationMessages.InvalidFileSize);

            // Assert
            result.ShouldBe(expectedMessage);
        }

        [Fact]
        public void GetStringWithFrenchLocalizationReturnsFrenchMessage()
        {
            // Arrange
            var expectedMessage = "La taille du fichier dépasse la limite maximale autorisée";
            var localizedString = new LocalizedString(ValidationMessages.InvalidFileSize, expectedMessage, false);
            _stringLocalizer[ValidationMessages.InvalidFileSize].Returns(localizedString);

            // Act
            var result = _localizationService.GetString(ValidationMessages.InvalidFileSize);

            // Assert
            result.ShouldBe(expectedMessage);
        }

        [Fact]
        public void GetStringWithInvalidKeyReturnsKey()
        {
            // Arrange
            const string invalidKey = "InvalidKey";
            var localizedString = new LocalizedString(invalidKey, invalidKey, true); // resourceNotFound = true
            _stringLocalizer[invalidKey].Returns(localizedString);

            // Act
            var result = _localizationService.GetString(invalidKey);

            // Assert
            result.ShouldBe(invalidKey);
        }

        [Fact]
        public void GetStringWithStatusMessageReturnsLocalizedStatus()
        {
            // Arrange
            var expectedMessage = "Le fichier a été téléchargé et est en file d'attente pour traitement";
            var localizedString = new LocalizedString(StatusMessages.Uploaded, expectedMessage, false);
            _stringLocalizer[StatusMessages.Uploaded].Returns(localizedString);

            // Act
            var result = _localizationService.GetString(StatusMessages.Uploaded);

            // Assert
            result.ShouldBe(expectedMessage);
        }

        [Fact]
        public void GetStringWithFormatParametersReturnsFormattedMessage()
        {
            // Arrange
            var key = "TestKey";
            var expectedMessage = "Hello John, you have 5 messages";
            var localizedString = new LocalizedString(key, expectedMessage, false);
            _stringLocalizer[key, "John", 5].Returns(localizedString);

            // Act
            var result = _localizationService.GetString(key, "John", 5);

            // Assert
            result.ShouldBe(expectedMessage);
        }
    }
}