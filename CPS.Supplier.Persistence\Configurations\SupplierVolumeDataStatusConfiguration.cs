﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierVolumeDataStatusConfiguration : IEntityTypeConfiguration<SupplierVolumeDataStatus>
    {
        public void Configure(EntityTypeBuilder<SupplierVolumeDataStatus> entity)
        {
            entity.HasKey(e => e.IntDataStatusId).IsClustered(false);

            entity.ToTable("SupplierVolumeDataStatus", "SPL");

            entity.Property(e => e.IntDataStatusId).HasColumnName("intDataStatusID");
            entity.Property(e => e.StrDataStatusName)
                .IsRequired()
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("strDataStatusName");
            entity.Property(e => e.StrDataStatusNameFr)
                .HasMaxLength(25)
                .IsUnicode(false)
                .HasColumnName("strDataStatusNameFR");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierVolumeDataStatus> entity);
    }
}
