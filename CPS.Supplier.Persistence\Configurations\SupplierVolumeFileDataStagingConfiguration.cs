﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierVolumeFileDataStagingConfiguration : IEntityTypeConfiguration<SupplierVolumeFileDataStaging>
    {
        public void Configure(EntityTypeBuilder<SupplierVolumeFileDataStaging> entity)
        {
            entity
                .HasNoKey()
                .ToTable("SupplierVolumeFileData_Staging", "SPL");

            entity.Property(e => e.Amount)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.AramarkCpsCustomerId)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("Aramark CPS Customer ID");
            entity.Property(e => e.City)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.CustomerLocationName)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("Customer (Location) Name");
            entity.Property(e => e.DistributorId)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("Distributor ID");
            entity.Property(e => e.ErrorDesc).IsUnicode(false);
            entity.Property(e => e.IntDataFileId).HasColumnName("intDataFileID");
            entity.Property(e => e.IntDataFileRowIdStaging)
                .ValueGeneratedOnAdd()
                .HasColumnName("intDataFileRowID_Staging");
            entity.Property(e => e.MonthOfPurchase)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("Month of Purchase");
            entity.Property(e => e.PostalZip)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("Postal Zip");
            entity.Property(e => e.Province)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.RecordNo)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("Record No");
            entity.Property(e => e.StreetAddress1)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("Street Address 1");
            entity.Property(e => e.StreetAddress2)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("Street Address 2");
            entity.Property(e => e.SupplierCustomerId)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("Supplier Customer ID");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierVolumeFileDataStaging> entity);
    }
}
