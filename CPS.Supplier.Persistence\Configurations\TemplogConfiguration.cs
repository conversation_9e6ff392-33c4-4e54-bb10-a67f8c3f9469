﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class TemplogConfiguration : IEntityTypeConfiguration<Templog>
    {
        public void Configure(EntityTypeBuilder<Templog> entity)
        {
            entity
                .HasNoKey()
                .ToTable("templog", "SPL");

            entity.Property(e => e.IntDataFileId).HasColumnName("intDataFileID");
            entity.Property(e => e.Intusersk).HasColumnName("intusersk");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<Templog> entity);
    }
}
