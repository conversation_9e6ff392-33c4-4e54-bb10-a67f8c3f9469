using System.Globalization;
using System.Text.Json;
using System.Text.RegularExpressions;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Common.Configuration;
using CPS.Supplier.Application.Exceptions;
using CPS.Supplier.Application.Interfaces.Infrastructure;
using CPS.Supplier.Application.Services.Localization;
using CPS.Supplier.Application.Services.VolumeFileProcess.Contracts;
using CPS.Supplier.Application.Services.VolumeFileProcess.Model;
using CPS.Supplier.Domain.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CPS.Supplier.Application.Services.VolumeFileProcess;

public class VolumeFileProcessService(
    IApplicationDbContext context,
    ISftpService sftpService,
    IAzureStorageService blobService,
    ILogger<VolumeFileProcessService> logger,
    IOptions<AzureStorageSettings> storageOptions,
    ILocalizationService localizationService) : IVolumeFileProcessService
{
    private readonly AzureStorageSettings _storageOptions = storageOptions.Value;

    [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "MA0051:Method is too long", Justification = "<Pending>")]
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "As per the requirement")]
    public async Task<FileProcessingResult> ProcessSupplierFileAsync(FileUploadMessage message, CancellationToken cancellationToken)
    {
        var result = new FileProcessingResult
        {
            CorrelationId = message.CorrelationId,
        };

        string? tempFilePath = null;

        try
        {
            logger.LogInformation("Starting supplier file processing. CorrelationId: {CorrelationId}, FileName: {FileName}",
                message.CorrelationId, message.FileName);

            ValidateMessage(message);

            logger.LogDebug("ValidateMessage passed.");

            // Extract blob name from full URL
            var blobName = ExtractBlobNameFromUrl(message.BlobPath);

            using var fileStream = await blobService.DownloadFileAsync(blobName, message.ContainerName, cancellationToken);

            logger.LogDebug("Downloaded fileStream.");

            var tempFileName = Path.GetRandomFileName();
            tempFilePath = Path.Combine(Path.GetTempPath(), tempFileName);

            logger.LogDebug("TempFilePath is: {TempFilePath}", tempFilePath);

            await using (var tempFileStream = File.Create(tempFilePath))
            {
                await fileStream.CopyToAsync(tempFileStream, cancellationToken);
            }

            logger.LogDebug("file copied to temp location");

            sftpService.UploadFile(tempFilePath, message.FileName);

            logger.LogDebug("File uploaded to sftp");

            await ArchiveAndDeleteOriginalFile(tempFilePath, message.FileName, message.SupplierId, message.CorrelationId, cancellationToken);

            logger.LogDebug("File archived");

            result.DataFileId = await ExecuteUploadFileAsync(message.FileName, message.ProcessingMonth, message.UserSK);
            result.ProcessingStatus = "Uploaded";

            result.ValidationErrors = await GetValidationErrorsAsync(result.DataFileId);

            if (result.ValidationErrors.Any())
            {
                await UpdateFileStausToFailedAsync(result.DataFileId, cancellationToken);

                // This will be handlded by the Cathch block
                throw new BadRequestException($"Supplier file processing completed successfully. CorrelationId: {message.CorrelationId}, DataFileId: {result.DataFileId}");
            }
            else
            {
                await UpdateFileStatusToExternalMappingAsync(result.DataFileId, cancellationToken);

                result.ProcessingStatus = "External Mapping";
                result.Success = true;

                logger.LogInformation("Supplier file processing completed successfully. CorrelationId: {CorrelationId}, DataFileId: {DataFileId}",
                    message.CorrelationId, result.DataFileId);
            }

            return result;
        }
        catch (Exception ex)
        {
            result.ErrorMessage = ex.Message;
            result.ProcessingStatus = "Upload Failed";
            result.Success = false;

            logger.LogError(ex, "Error processing supplier file. CorrelationId: {CorrelationId}, FileName: {FileName}",
                message.CorrelationId, message.FileName);

            logger.LogDebug("Error processing supplier file exception {Exception}", JsonSerializer.Serialize(ex));

            logger.LogDebug("Error processing supplier file exception {Result}", JsonSerializer.Serialize(result));

            return result;
        }
        finally
        {
            if (tempFilePath != null && File.Exists(tempFilePath))
            {
                try
                {
                    File.Delete(tempFilePath);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Failed to delete temporary file: {TempFilePath}", tempFilePath);
                }
            }
        }
    }

    private async Task<int> ExecuteUploadFileAsync(string fileName, string processingMonth, int userSK)
    {
        var parameters = new[]
        {
            new SqlParameter("@strFileName", fileName),
            new SqlParameter("@strMonth", processingMonth),
            new SqlParameter("@intUserSk", userSK),
        };

        await context.Database.ExecuteSqlRawAsync("EXEC SPL.SPL_UploadFile @strFileName, @strMonth, @intUserSk", parameters);

        var dataFileId = await GetDataFileIdByFileNameAsync(fileName);

        return dataFileId;
    }

    private async Task<IEnumerable<ValidationError>> GetValidationErrorsAsync(int dataFileId)
    {
        var errors = await context.GetDbSet<SupplierVolumeFileError>()
            .Where(e => e.IntDataFileId == dataFileId)
            .Select(e => new ValidationError
            {
                ErrorTypeId = e.IntErrorTypeId,
                ErrorDescription = e.StrSqlerror ?? string.Empty,
                LineNumber = e.IntRowNo,
                ColumnName = e.StrColumnName ?? string.Empty,
                InvalidValue = string.Empty,
            })
            .ToListAsync();

        return errors;
    }

    private async Task<int> GetDataFileIdByFileNameAsync(string fileName)
    {
        var supplierVolumeFile = await context.GetDbSet<SupplierVolumeFile>()
            .Where(f => f.StrFileName == fileName)
            .OrderByDescending(f => f.DttmUploadDate)
            .FirstOrDefaultAsync();

        if (supplierVolumeFile == null)
        {
            throw new InvalidOperationException($"No SupplierVolumeFile found with filename: {fileName}");
        }

        return supplierVolumeFile.IntDataFileId;
    }

    private static string ExtractBlobNameFromUrl(string blobUrl)
    {
        // Extract blob name from full URL
        // Example: https://account.blob.core.windows.net/container/path/file.csv -> path/file.csv
        var uri = new Uri(blobUrl);
        var segments = uri.Segments;

        // Skip the first segment (/) and container name, get the rest
        if (segments.Length > 2)
        {
            var blobPath = string.Join("", segments.Skip(2)).TrimStart('/');
            return Uri.UnescapeDataString(blobPath);
        }

        throw new BadRequestException($"Invalid blob URL format: {blobUrl}");
    }

    [System.Diagnostics.CodeAnalysis.SuppressMessage("Security", "MA0009:Add regex evaluation timeout", Justification = "This can be ignored as this is a background processing")]
    private void ValidateMessage(FileUploadMessage message)
    {
        if (string.IsNullOrWhiteSpace(message.FileName))
            throw new BadRequestException(localizationService.GetString(ValidationMessages.FileNameRequired));

        if (string.IsNullOrWhiteSpace(message.BlobPath))
            throw new BadRequestException(localizationService.GetString(ValidationMessages.BlobPathRequired));

        if (string.IsNullOrWhiteSpace(message.ContainerName))
            throw new BadRequestException(localizationService.GetString(ValidationMessages.ContainerNameRequired));

        if (string.IsNullOrWhiteSpace(message.ProcessingMonth))
            throw new BadRequestException(localizationService.GetString(ValidationMessages.ProcessingMonthRequiredWithFormat));

        if (message.UserSK <= 0)
            throw new BadRequestException(localizationService.GetString(ValidationMessages.UserSKRequired));

        if (message.SupplierId <= 0)
            throw new BadRequestException(localizationService.GetString(ValidationMessages.SupplierIdRequired));

        if (!Regex.IsMatch(message.ProcessingMonth, @"^\d{6}$"))
            throw new BadRequestException(localizationService.GetString(ValidationMessages.ProcessingMonthInvalidFormat));
    }

    private async Task ArchiveAndDeleteOriginalFile(string tempFilePath, string fileName, int supplierId, string correlationId, CancellationToken cancellationToken)
    {
        var archiveContainerName = _storageOptions.ArchivedFilesContainer;
        if (string.IsNullOrWhiteSpace(archiveContainerName))
        {
            logger.LogWarning("Archive container name not configured. Skipping file archival and deletion. CorrelationId: {CorrelationId}", correlationId);
            return;
        }

        var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss", CultureInfo.InvariantCulture);
        // Archive files are stored with supplier prefix for organization
        var archiveBlobPath = $"{supplierId.ToString(CultureInfo.InvariantCulture)}/{timestamp}_{fileName}";
        // Original files are stored at root level without supplier prefix
        var originalFileBlobPath = fileName;

        logger.LogInformation("Starting file archival and deletion process. CorrelationId: {CorrelationId}, FileName: {FileName}", correlationId, fileName);

        // Archive the file using the temp file, then delete original
        await using var fileStream = File.OpenRead(tempFilePath);
        var metadata = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            ["supplierId"] = supplierId.ToString(CultureInfo.InvariantCulture),
            ["correlationId"] = correlationId,
            ["archived"] = "true"
        };
        await blobService.UploadFileAsync(fileStream, archiveBlobPath, _storageOptions.ArchivedFilesContainer, metadata, cancellationToken);
        await blobService.DeleteFileAsync(originalFileBlobPath, _storageOptions.BlobContainerName, cancellationToken);
    }
    private async Task UpdateFileStausToFailedAsync(int dataFileId, CancellationToken cancellationToken)
    {
        var supplierVolumeFile = await context.GetDbSet<SupplierVolumeFile>().FindAsync([dataFileId], cancellationToken);

        if (supplierVolumeFile == null)
        {
            throw new NotFoundException("IntDataFileId not found while updating failed status for the file");
        }

        supplierVolumeFile.IntFileStatusId = (int)FileStatus.UploadFailed;

        context.GetDbSet<SupplierVolumeFile>().Update(supplierVolumeFile);

        await context.SaveChangesAsync(cancellationToken);
    }

    private async Task UpdateFileStatusToExternalMappingAsync(int dataFileId, CancellationToken cancellationToken)
    {
        var supplierVolumeFile = await context.GetDbSet<SupplierVolumeFile>().FindAsync([dataFileId], cancellationToken);

        if (supplierVolumeFile == null)
        {
            throw new NotFoundException("IntDataFileId not found while updating status to External Mapping");
        }

        supplierVolumeFile.IntFileStatusId = (int)FileStatus.ExternalMapping;

        context.GetDbSet<SupplierVolumeFile>().Update(supplierVolumeFile);

        await context.SaveChangesAsync(cancellationToken);

        logger.LogInformation("File status updated to External Mapping. DataFileId: {DataFileId}", dataFileId);
    }

} // End of class