﻿using CPS.Supplier.Application.Models.Mail;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace CPS.Supplier.Infrastructure.Mail
{
    public class EmailService : IEmailService
    {
        public EmailSettings EmailSettings { get; }
        public ILogger<EmailService> Logger { get; }

        public EmailService(IOptions<EmailSettings> mailSettings, ILogger<EmailService> logger)
        {
            ArgumentNullException.ThrowIfNull(mailSettings);
            EmailSettings = mailSettings.Value;
            Logger = logger;
        }

        //This is a dummy method to send email
        public bool SendEmail()
        {
            return true;
        }

        //In the real project use this method to send email
        public async Task<bool> SendEmail(Email email)
        {
            ArgumentNullException.ThrowIfNull(email);
            var client = new SendGridClient(EmailSettings.ApiKey);

            var subject = email.Subject;
            var to = new EmailAddress(email.To);
            var emailBody = email.Body;

            var from = new EmailAddress
            {
                Email = EmailSettings.FromAddress,
                Name = EmailSettings.FromName
            };

            var sendGridMessage = MailHelper.CreateSingleEmail(from, to, subject, emailBody, emailBody);
            var response = await client.SendEmailAsync(sendGridMessage);

            Logger.LogInformation("Email sent");

            if (response.StatusCode == System.Net.HttpStatusCode.Accepted || response.StatusCode == System.Net.HttpStatusCode.OK)
                return true;

            Logger.LogError("Email sending failed");

            return false;
        }
    }
}