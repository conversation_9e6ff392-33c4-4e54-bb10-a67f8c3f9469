namespace CPS.Supplier.Application.Services.PasswordReset.Dto;

public class ValidatePasswordResetTokenResponseDto
{
    public bool IsValid { get; set; }

    public string? Username { get; set; }

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public int LanguageId { get; set; }

    public string? ErrorMessage { get; set; }

    public static ValidatePasswordResetTokenResponseDto Success(string username, string firstName, string lastName, int languageId)
        => new() { IsValid = true, Username = username, FirstName = firstName, LastName = lastName, LanguageId = languageId };

    public static ValidatePasswordResetTokenResponseDto Failed(string errorMessage)
        => new() { IsValid = false, ErrorMessage = errorMessage };
}