using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Renci.SshNet;

namespace CPS.Supplier.Infrastructure.Health
{
    public class SftpHealthCheck : IHealthCheck
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SftpHealthCheck> _logger;

        public SftpHealthCheck(IConfiguration configuration, ILogger<SftpHealthCheck> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                var host = _configuration["Sftp:Host"] ?? throw new InvalidOperationException("SFTP host not configured.");
                var port = int.TryParse(_configuration["Sftp:Port"], System.Globalization.NumberStyles.Integer, System.Globalization.CultureInfo.InvariantCulture, out var p) ? p : 22;
                var username = _configuration["Sftp:Username"] ?? throw new InvalidOperationException("SFTP username not configured.");
                var password = _configuration["Sftp:Password"] ?? throw new InvalidOperationException("SFTP password not configured.");

                using var client = new SftpClient(host, port, username, password);
                client.Connect();
                if (client.IsConnected)
                {
                    _logger.LogInformation("SFTP connection successful to {Host}:{Port}", host, port);
                    client.Disconnect();
                    return Task.FromResult(HealthCheckResult.Healthy("SFTP connection successful."));
                }
                _logger.LogWarning("SFTP connection failed to {Host}:{Port}", host, port);
                return Task.FromResult(HealthCheckResult.Unhealthy("SFTP connection failed."));
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "SFTP configuration error: {Message}", ex.Message);
                return Task.FromResult(HealthCheckResult.Unhealthy($"SFTP configuration error: {ex.Message}"));
            }
            catch (Renci.SshNet.Common.SshException ex)
            {
                _logger.LogError(ex, "SFTP SSH error to {Host}:{Port}", _configuration["Sftp:Host"], _configuration["Sftp:Port"]);
                return Task.FromResult(HealthCheckResult.Unhealthy($"SFTP SSH error: {ex.Message}"));
            }
        }
    }
}