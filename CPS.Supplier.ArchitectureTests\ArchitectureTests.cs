﻿using System.Reflection;
using ArchUnitNET.Fluent;
using ArchUnitNET.Loader;
using ArchUnitNET.xUnit;
using Xunit;

using Assembly = System.Reflection.Assembly;

namespace CPS.Supplier.ArchitectureTests
{
    public class ArchitectureTests
    {
        private const string ApiNamespace = "CPS.Supplier.Api";
        private const string ApplicationNamespace = "CPS.Supplier.Application";
        private const string DomainNamespace = "CPS.Supplier.Domain";
        private const string InfrastructureNamespace = "CPS.Supplier.Infrastructure";
        private const string PersistenceNamespace = "CPS.Supplier.Persistence";

        // Load your architecture once
        private static readonly ArchUnitNET.Domain.Architecture Architecture = new ArchLoader()
            .LoadAssemblies(
                Assembly.Load(ApiNamespace),
                Assembly.Load(ApplicationNamespace),
                Assembly.Load(DomainNamespace),
                Assembly.Load(InfrastructureNamespace),
                Assembly.Load(PersistenceNamespace))
            .Build();

        #region Clean Architecture Dependency Rules

        [Fact]
        public void DomainShouldNotDependOnAnyOtherLayer()
        {
            // Test each dependency rule separately
            var rule1 = ArchRuleDefinition.Types()
                .That().ResideInNamespace(DomainNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(ApiNamespace, true);

            var rule2 = ArchRuleDefinition.Types()
                .That().ResideInNamespace(DomainNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(ApplicationNamespace, true);

            var rule3 = ArchRuleDefinition.Types()
                .That().ResideInNamespace(DomainNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(InfrastructureNamespace, true);

            var rule4 = ArchRuleDefinition.Types()
                .That().ResideInNamespace(DomainNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(PersistenceNamespace, true);

            rule1.Check(Architecture);
            rule2.Check(Architecture);
            rule3.Check(Architecture);
            rule4.Check(Architecture);
        }

        [Fact]
        public void ApplicationShouldNotDependOnApiOrInfrastructure()
        {
            // Test each dependency rule separately
            var rule1 = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(ApiNamespace, true);

            var rule2 = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(InfrastructureNamespace, true);

            var rule3 = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(PersistenceNamespace, true);

            rule1.Check(Architecture);
            rule2.Check(Architecture);
            rule3.Check(Architecture);
        }

        [Fact]
        public void ApplicationCanDependOnDomain()
        {
            // Test that Application is allowed to depend on Domain - positive validation
            // This verifies that the Clean Architecture allows Application -> Domain dependencies

            // Test that no rule violation occurs when Application references Domain
            // We validate this by ensuring we don't find any violations of this dependency
            var applicationTypes = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true);

            // Get all Application types and verify the architecture allows Domain references
            var typesToCheck = applicationTypes.GetObjects(Architecture).ToList();

            // If we have Application types, the architecture should allow Domain dependencies
            // This is a positive test confirming the allowed dependency direction
            Assert.True(typesToCheck.Count > 0, "Application layer should contain types that can reference Domain layer");

            // Additional verification: ensure we're not blocking Domain dependencies
            var domainDependencyRule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(PersistenceNamespace, true);

            // This validates Application doesn't depend on Persistence (which would be wrong)
            // while allowing Domain dependencies (which is correct in Clean Architecture)
            domainDependencyRule.Check(Architecture);
        }

        [Fact]
        public void InfrastructureShouldNotDependOnApi()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(InfrastructureNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(ApiNamespace, true);

            rule.Check(Architecture);
        }

        [Fact]
        public void PersistenceShouldNotDependOnApi()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(PersistenceNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(ApiNamespace, true);

            rule.Check(Architecture);
        }

        #endregion

        #region Domain Layer Rules

        [Fact]
        public void ValueObjectsShouldBeImmutable()
        {
            var valueObjectTypes = ArchRuleDefinition.Classes()
                .That().ResideInNamespace($"{DomainNamespace}.ValueObjects", true)
                .As("Value Objects");

            foreach (var type in valueObjectTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(DomainNamespace);
                var actualType = assembly.GetType(type.FullName);

                if (actualType != null)
                {
                    var publicSetters = actualType.GetProperties()
                        .Where(p => p.SetMethod != null && p.SetMethod.IsPublic)
                        .ToList();

                    Assert.Empty(publicSetters);
                }
            }
        }

        #endregion

        #region API Project Rules

        [Fact]
        public void EndpointClassesShouldImplementIEndpointGroupBase()
        {
            // Arrange
            var endpointTypes = ArchRuleDefinition.Classes()
                .That()
                .ResideInNamespace($"{ApiNamespace}.Endpoints", true)
                .As("Endpoint Classes");

            // Act & Assert
            foreach (var type in endpointTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApiNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.GetInterfaces().Any(i => i.FullName == $"{ApiNamespace}.Infrastructure.IEndpointGroupBase"),
                    $"Type {type.FullName} should implement IEndpointGroupBase");
            }
        }

        [Fact]
        public void ExtensionClassesShouldBeStaticAndEndWithExtensions()
        {
            // Arrange
            var extensionTypes = ArchRuleDefinition.Classes()
                .That()
                .ResideInNamespace($"{ApiNamespace}.Extensions", true)
                .As("Extension Classes");

            // Act & Assert
            foreach (var type in extensionTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApiNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.Name.EndsWith("Extensions", StringComparison.Ordinal),
                    $"Type {type.FullName} should end with 'Extensions'");
                Assert.True(actualType != null && actualType.IsSealed && actualType.IsAbstract,
                    $"Type {type.FullName} should be static (sealed and abstract)");

                var nonStaticMethods = actualType!.GetMethods()
                    .Where(m => !m.IsStatic && !IsInheritedFromObject(m))
                    .ToList();
                Assert.Empty(nonStaticMethods);
            }
        }

        [Fact]
        public void InfrastructureFolderShouldContainOnlyInterfaces()
        {
            // Arrange
            var infrastructureTypes = ArchRuleDefinition.Types()
                .That()
                .ResideInNamespace($"{ApiNamespace}.Infrastructure");

            // Act & Assert
            foreach (var type in infrastructureTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApiNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.IsInterface,
                    $"Type {type.FullName} should be an interface");
            }
        }

        #endregion

        #region Application Project Rules - Exceptions

        [Fact]
        public void ExceptionClassesShouldEndWithExceptionAndInheritExceptionClass()
        {
            // Arrange
            var exceptionTypes = ArchRuleDefinition.Classes()
                .That()
                .ResideInNamespace($"{ApplicationNamespace}.Exceptions", true)
                .As("Exception Classes");

            // Act & Assert
            foreach (var type in exceptionTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApplicationNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.Name.EndsWith("Exception", StringComparison.Ordinal),
                    $"Type {type.FullName} should end with 'Exception'");
                Assert.True(actualType != null && actualType.IsSubclassOf(typeof(Exception)),
                    $"Type {type.FullName} should inherit from Exception class");
            }
        }

        #endregion

        #region Application Project Rules - Interfaces

        [Fact]
        public void AllInterfacesShouldStartWithI()
        {
            // Arrange
            var allInterfaces = ArchRuleDefinition.Interfaces()
                .That()
                .ResideInNamespace(ApiNamespace, true)
                .Or().ResideInNamespace(ApplicationNamespace, true)
                .Or().ResideInNamespace(DomainNamespace, true)
                .Or().ResideInNamespace(InfrastructureNamespace, true)
                .Or().ResideInNamespace(PersistenceNamespace, true)
                .As("All Interfaces");

            // Act & Assert
            foreach (var type in allInterfaces.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(type.Assembly.FullName);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.Name.StartsWith('I'),
                    $"Type {type.FullName} should start with 'I'");
            }
        }

        #endregion

        #region Application Project Rules - Services

        [Fact]
        public void DtoClassesShouldEndWithDto()
        {
            // Arrange
            var dtoTypes = ArchRuleDefinition.Classes()
                .That()
                .ResideInNamespace($"{ApplicationNamespace}.Services.Dto", true)
                .As("DTO Classes");

            // Act & Assert
            foreach (var type in dtoTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApplicationNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.Name.EndsWith("Dto", StringComparison.Ordinal),
                    $"Type {type.FullName} should end with 'Dto'");
            }
        }

        [Fact]
        public void DtoFileNamesShouldEndWithDto()
        {
            // Arrange
            var dtoNamespace = $"{ApplicationNamespace}.Services.Dto";
            var assembly = Assembly.Load(ApplicationNamespace);

            // Get all types in the DTO namespace
            var dtoTypes = assembly.GetTypes()
                .Where(t => t.IsClass && t.Namespace != null && t.Namespace.StartsWith(dtoNamespace, StringComparison.Ordinal))
                .ToList();

            foreach (var type in dtoTypes)
            {
                Console.WriteLine($"Checking type: {type.FullName}");
                Console.WriteLine($"Type Name: {type.Name}");
                // Fallback to convention for file name validation
                var expectedFileName = $"{type.Name}.cs";
                Assert.True(type.Name.EndsWith("Dto", StringComparison.Ordinal),
                    $"Type {type.FullName} should end with 'Dto'");

                // Optionally, check file name if you have a way to map type to file
                // (This is not always possible in release builds or without SourceLink)
                Assert.True(expectedFileName.EndsWith("Dto.cs", StringComparison.Ordinal),
                    $"File for type {type.FullName} should end with 'Dto.cs'");
            }
        }

        [Fact]
        public void DtoClassesShouldNotHaveMethods()
        {
            var dtoTypes = ArchRuleDefinition.Classes()
                .That().ResideInNamespace($"{ApplicationNamespace}.Services.Dto", true)
                .As("DTO Classes");

            foreach (var type in dtoTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApplicationNamespace);
                var actualType = assembly.GetType(type.FullName);

                var methodCount = actualType?.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly)
                    .Count(m => !IsInheritedFromObject(m)) ?? 0;

                Assert.True(methodCount == 0, $"DTO {type.FullName} should not contain business logic (methods)");
            }
        }

        [Fact]
        public void CommandClassesShouldEndWithCommand()
        {
            // Arrange
            var commandTypes = ArchRuleDefinition.Classes()
                .That()
                .ResideInNamespace($"{ApplicationNamespace}.Services.Commands", true)
                .As("Command Classes");

            // Act & Assert
            foreach (var type in commandTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApplicationNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.Name.EndsWith("Command", StringComparison.Ordinal),
                    $"Type {type.FullName} should end with 'Command'");
            }
        }

        [Fact]
        public void ContractClassesShouldBeInterfacesAndStartWithI()
        {
            // Arrange
            var contractTypes = ArchRuleDefinition.Types()
                .That()
                .ResideInNamespace($"{ApplicationNamespace}.Services.Contracts", true)
                .As("Contract Types");

            // Act & Assert
            foreach (var type in contractTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApplicationNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.IsInterface,
                    $"Type {type.FullName} should be an interface");
                Assert.True(actualType != null && actualType.Name.StartsWith('I'),
                    $"Type {type.FullName} should start with 'I'");
            }
        }

        [Fact]
        public void QueryClassesShouldEndWithQuery()
        {
            // Arrange
            var queryTypes = ArchRuleDefinition.Classes()
                .That()
                .ResideInNamespace($"{ApplicationNamespace}.Services.Queries", true)
                .As("Query Classes");

            // Act & Assert
            foreach (var type in queryTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApplicationNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.Name.EndsWith("Query", StringComparison.Ordinal),
                    $"Type {type.FullName} should end with 'Query'");
            }
        }

        [Fact]
        public void ValidatorClassesShouldEndWithValidatorAndInheritAbstractValidator()
        {
            // Arrange
            var validatorTypes = ArchRuleDefinition.Classes()
                .That()
                .ResideInNamespace($"{ApplicationNamespace}.Services.*.Validators", true)
                .As("Validator Classes");

            // Act & Assert
            foreach (var type in validatorTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(ApplicationNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.Name.EndsWith("Validator", StringComparison.Ordinal),
                    $"Type {type.FullName} should end with 'Validator'");
                Assert.True(actualType != null && actualType.BaseType != null && actualType.BaseType.Name.Contains("AbstractValidator", StringComparison.Ordinal),
                    $"Type {type.FullName} should inherit from AbstractValidator");
            }
        }

        #endregion

        #region Infrastructure Project Rules

        [Fact]
        public void ConfigurationClassesShouldEndWithConfigurationAndImplementIEntityTypeConfiguration()
        {
            // Arrange
            var configurationTypes = ArchRuleDefinition.Classes()
                .That()
                .ResideInNamespace($"{PersistenceNamespace}.Configuration", true)
                .As("Configuration Classes");

            // Act & Assert
            foreach (var type in configurationTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(PersistenceNamespace);
                var actualType = assembly.GetType(type.FullName);

                Assert.True(actualType != null && actualType.Name.EndsWith("Configuration", StringComparison.Ordinal),
                    $"Type {type.FullName} should end with 'Configuration'");
                Assert.True(actualType != null && actualType.GetInterfaces().Any(i => i.Name.Contains("IEntityTypeConfiguration", StringComparison.Ordinal)),
                    $"Type {type.FullName} should implement IEntityTypeConfiguration");
            }
        }

        #endregion

        #region Layer Dependency Rules

        [Fact]
        public void ApplicationLayerShouldNotDependOnApiLayer()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(ApiNamespace, true);

            rule.Check(Architecture);
        }

        [Fact]
        public void ApplicationLayerShouldNotDependOnInfrastructureLayer()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(InfrastructureNamespace, true);

            rule.Check(Architecture);
        }

        [Fact]
        public void ApiLayerShouldNotDependOnDomainDirectly()
        {
            // API layer should depend on Application layer, but NOT directly on Domain
            // This ensures proper layering: API -> Application -> Domain
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApiNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(DomainNamespace, true);

            rule.Check(Architecture);
        }

        [Fact]
        public void DomainShouldNotDependOnApplication()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(DomainNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(ApplicationNamespace, true)
                .WithoutRequiringPositiveResults();
            rule.Check(Architecture);
        }

        [Fact]
        public void DomainShouldNotDependOnApi()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(DomainNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(ApiNamespace, true)
                .WithoutRequiringPositiveResults();
            rule.Check(Architecture);
        }

        [Fact]
        public void DomainShouldNotDependOnInfrastructure()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(DomainNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(InfrastructureNamespace, true)
                .WithoutRequiringPositiveResults();
            rule.Check(Architecture);
        }

        [Fact]
        public void DomainShouldNotDependOnPersistence()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(DomainNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(PersistenceNamespace, true)
                .WithoutRequiringPositiveResults();
            rule.Check(Architecture);
        }

        [Fact]
        public void ApplicationShouldNotDependOnApi()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(ApiNamespace, true)
                .WithoutRequiringPositiveResults();
            rule.Check(Architecture);
        }

        [Fact]
        public void ApplicationShouldNotDependOnInfrastructure()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(InfrastructureNamespace, true)
                .WithoutRequiringPositiveResults();
            rule.Check(Architecture);
        }

        [Fact]
        public void ApplicationShouldNotDependOnPersistence()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApplicationNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(PersistenceNamespace, true)
                .WithoutRequiringPositiveResults();
            rule.Check(Architecture);
        }

        [Fact]
        public void InfrastructureShouldNotDependOnPersistence()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(InfrastructureNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(PersistenceNamespace, true)
                .WithoutRequiringPositiveResults();
            rule.Check(Architecture);
        }

        [Fact]
        public void PersistenceShouldNotDependOnInfrastructure()
        {
            var rule = ArchRuleDefinition.Types()
                .That().ResideInNamespace(PersistenceNamespace, true)
                .Should().NotDependOnAnyTypesThat().ResideInNamespace(InfrastructureNamespace, true)
                .WithoutRequiringPositiveResults();
            rule.Check(Architecture);
        }

        [Fact]
        public void ProductionShouldNotReferenceTestProjects()
        {
            var assemblies = new[]
            {
                Assembly.Load(ApiNamespace),
                Assembly.Load(ApplicationNamespace),
                Assembly.Load(DomainNamespace),
                Assembly.Load(InfrastructureNamespace),
                Assembly.Load(PersistenceNamespace)
            };

            foreach (var assembly in assemblies)
            {
                var referencedAssemblies = assembly.GetReferencedAssemblies();
                var testReferences = referencedAssemblies
                    .Where(a => a.Name?.Contains("Test", StringComparison.OrdinalIgnoreCase) == true ||
                               a.Name?.Contains("Mock", StringComparison.OrdinalIgnoreCase) == true)
                    .ToList();

                Assert.Empty(testReferences);
            }
        }

        [Fact]
        public void NoPublicFields()
        {
            var allTypes = ArchRuleDefinition.Types()
                .That().ResideInNamespace(ApiNamespace, true)
                .Or().ResideInNamespace(DomainNamespace, true)
                .Or().ResideInNamespace(InfrastructureNamespace, true)
                .Or().ResideInNamespace(PersistenceNamespace, true);

            foreach (var type in allTypes.GetObjects(Architecture))
            {
                var assembly = Assembly.Load(type.Assembly.FullName);
                var actualType = assembly.GetType(type.FullName);

                if (actualType != null)
                {
                    var publicFields = actualType.GetFields(BindingFlags.Public | BindingFlags.Instance)
                        .Where(f => !f.IsStatic)
                        .ToList();

                    Assert.Empty(publicFields);
                }
            }
        }

        #endregion

        private static bool IsInheritedFromObject(MethodInfo method)
        {
            return method.DeclaringType == typeof(object);
        }
    }
}