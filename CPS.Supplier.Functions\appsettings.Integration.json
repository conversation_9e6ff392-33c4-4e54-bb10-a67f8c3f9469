{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"SupplierConnectionString": ""}, "ApplicationInsights": {"ConnectionString": ""}, "SFTP": {"Host": "***********", "Port": "22", "Username": "", "Password": "", "BasePath": "/FTPSupplier/DEV/InputVolumeFiles/", "VtrakPath": "/vtrak/supplier-files"}, "EnabledApplicationInsightsLogger": "true", "EnabledApplicationInsightsMetrics": "true", "KeyVault": {"Enabled": true, "Endpoint": ""}, "SupplierArchivedFileBlobContainer": "supplier-archived-files", "AzureStorage": {"ConnectionString": "", "BlobContainerName": "supplier-incoming-files", "QueueName": "supplier-incoming-messages"}}