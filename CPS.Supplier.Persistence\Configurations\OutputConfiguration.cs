﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class OutputConfiguration : IEntityTypeConfiguration<Output>
    {
        public void Configure(EntityTypeBuilder<Output> entity)
        {
            entity
                .HasNoKey()
                .ToTable("output", "SPL");

            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("id");
            entity.Property(e => e.Output1)
                .HasMaxLength(255)
                .HasColumnName("output");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<Output> entity);
    }
}
