﻿using CPS.Supplier.Api.Infrastructure;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.PasswordReset.Commands;
using CPS.Supplier.Application.Services.PasswordReset.Contracts;
using CPS.Supplier.Application.Services.PasswordReset.Dto;

namespace CPS.Supplier.Api.Endpoints;

public class PasswordReset : IEndpointGroupBase
{
    public void Map(WebApplication app)
    {
        var group = app.MapGroup(this);
        group.MapPost(RequestPasswordReset, "/request");
        group.MapPost(ResetPassword, "/reset");
        group.MapPost(ValidatePasswordResetToken, "/validate");
    }

    private static async Task<IResult> RequestPasswordReset(
        PasswordResetRequestCommand command,
        IPasswordResetService passwordResetService,
        IMetricsService metricsService,
        CancellationToken cancellationToken)
    {
        metricsService.TrackMetric("PasswordResetApi.request.Count", 1);

        var response = await passwordResetService.RequestPasswordResetAsync(command, cancellationToken);

        if (response.Success)
        {
            return Results.Ok(response);
        }

        return Results.BadRequest(response);
    }

    private static async Task<IResult> ResetPassword(
        PasswordResetCommand command,
        IPasswordResetService passwordResetService,
        IMetricsService metricsService,
        CancellationToken cancellationToken)
    {
        metricsService.TrackMetric("PasswordResetApi.reset.Count", 1);

        var response = await passwordResetService.ResetPasswordAsync(command, cancellationToken);

        if (response.Success)
        {
            return Results.Ok(response);
        }

        return Results.BadRequest(response);
    }

    private static async Task<ValidatePasswordResetTokenResponseDto> ValidatePasswordResetToken(
        ValidatePasswordResetTokenCommand command,
        IPasswordResetService passwordResetService,
        IMetricsService metricsService,
        CancellationToken cancellationToken)
    {
        metricsService.TrackMetric("PasswordResetApi.validate.Count", 1);

        var response = await passwordResetService.ValidatePasswordResetTokenAsync(command, cancellationToken);

        return response;
    }
}