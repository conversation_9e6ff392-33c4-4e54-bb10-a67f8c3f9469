using System.Collections.ObjectModel;
using System.Globalization;
using System.Security.Claims;
using CPS.Supplier.Application.Common;

namespace CPS.Supplier.Application.Extensions;

public static class ClaimsPrincipalExtensions
{
    public static int? GetUserId(this ClaimsPrincipal principal)
    {
        var userIdClaim = principal.FindFirst(CustomClaimTypes.UserId)?.Value;
        return int.TryParse(userIdClaim, NumberStyles.Integer, CultureInfo.InvariantCulture, out var userId) ? userId : null;
    }

    public static string? GetUsername(this ClaimsPrincipal principal)
    {
        return principal.FindFirst(CustomClaimTypes.Username)?.Value;
    }

    public static ICollection<string> GetRoles(this ClaimsPrincipal principal)
    {
        var roles = principal.FindAll(ClaimTypes.Role)
            .Select(c => c.Value)
            .Where(role => !string.IsNullOrEmpty(role))
            .ToList();
        return new ReadOnlyCollection<string>(roles);
    }

    public static ICollection<string> GetPermissions(this ClaimsPrincipal principal)
    {
        var permissions = principal.FindAll(CustomClaimTypes.Permission)
            .Select(c => c.Value)
            .Where(permission => !string.IsNullOrEmpty(permission))
            .ToList();
        return new ReadOnlyCollection<string>(permissions);
    }

    public static ICollection<string> GetModules(this ClaimsPrincipal principal)
    {
        var modules = principal.FindAll(CustomClaimTypes.Module)
            .Select(c => c.Value)
            .Where(module => !string.IsNullOrEmpty(module))
            .ToList();
        return new ReadOnlyCollection<string>(modules);
    }

    public static int? GetSupplierId(this ClaimsPrincipal principal)
    {
        var supplierIdClaim = principal.FindFirst(CustomClaimTypes.SupplierId)?.Value;
        return int.TryParse(supplierIdClaim, NumberStyles.Integer, CultureInfo.InvariantCulture, out var supplierId) ? supplierId : null;
    }

    public static int? GetFacilityId(this ClaimsPrincipal principal)
    {
        var facilityIdClaim = principal.FindFirst(CustomClaimTypes.FacilityId)?.Value;
        return int.TryParse(facilityIdClaim, NumberStyles.Integer, CultureInfo.InvariantCulture, out var facilityId) ? facilityId : null;
    }

    public static bool HasRole(this ClaimsPrincipal principal, string roleName)
    {
        return principal.IsInRole(roleName);
    }

    public static bool HasPermission(this ClaimsPrincipal principal, string permissionName)
    {
        return principal.FindAll(CustomClaimTypes.Permission)
            .Any(c => string.Equals(c.Value, permissionName, StringComparison.OrdinalIgnoreCase));
    }

    public static bool HasModuleAccess(this ClaimsPrincipal principal, string moduleName)
    {
        return principal.FindAll(CustomClaimTypes.Module)
            .Any(c => string.Equals(c.Value, moduleName, StringComparison.OrdinalIgnoreCase));
    }

    public static bool HasAnyRole(this ClaimsPrincipal principal, params string[] roleNames)
    {
        return roleNames.Any(role => principal.IsInRole(role));
    }

    public static bool HasAnyPermission(this ClaimsPrincipal principal, params string[] permissionNames)
    {
        var userPermissions = principal.GetPermissions();
        return permissionNames.Any(permission =>
            userPermissions.Any(up => string.Equals(up, permission, StringComparison.OrdinalIgnoreCase)));
    }

    public static IDictionary<string, string> GetCustomClaims(this ClaimsPrincipal principal, string prefix = "custom:")
    {
        return principal.Claims
            .Where(c => c.Type.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
            .ToDictionary(c => c.Type[prefix.Length..], c => c.Value, StringComparer.Ordinal);
    }
}