﻿using System.Diagnostics;
using System.Globalization;
using System.Text;
using CPS.Supplier.Application.Common;

namespace CPS.Supplier.Api.Middleware;

public class RequestPerformanceMiddleware(
        RequestDelegate next,
        ILogger<RequestPerformanceMiddleware> logger,
        IMetricsService metricsService)
{

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var method = context.Request.Method;
        var path = context.Request.Path.Value ?? "unknown";

        logger.LogDebug("Starting request processing for {Method} {Path}", method, path);

        await LogRequestDataIfEnabled(context);

        TrackRequestCount(method, path);

        try
        {
            await next(context);

            logger.LogDebug("Request processing completed successfully for {Method} {Path}", method, path);
        }
        finally
        {
            stopwatch.Stop();
            var statusCode = context.Response.StatusCode;
            var elapsedMs = stopwatch.ElapsedMilliseconds;

            logger.LogDebug("Request timing completed: {Method} {Path} took {ElapsedMs}ms", method, path, elapsedMs);

            LogRequest(method, path, statusCode, elapsedMs);

            TrackRequestMetrics(method, path, statusCode, elapsedMs);
        }
    }

    private void TrackRequestCount(string method, string path)
    {
        logger.LogDebug("Tracking request count for {Method} {Path}", method, path);

        metricsService.TrackMetric("Api.Requests.Count", 1, new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            ["Method"] = method,
            ["Path"] = path
        });

        logger.LogDebug("Successfully tracked request count metric for {Method} {Path}", method, path);
    }

    private void LogRequest(string method, string path, int statusCode, long elapsedMs)
    {
        logger.LogInformation("HTTP {Method} {Path} responded {StatusCode} in {ElapsedMs}ms",
            method, path, statusCode, elapsedMs);
    }

    private void TrackRequestMetrics(string method, string path, int statusCode, long elapsedMs)
    {
        logger.LogDebug("Tracking metrics for {Method} {Path} - StatusCode: {StatusCode}, Duration: {ElapsedMs}ms",
            method, path, statusCode, elapsedMs);

        var properties = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            ["Method"] = method,
            ["Path"] = path,
            ["StatusCode"] = statusCode.ToString(CultureInfo.InvariantCulture)
        };

        metricsService.TrackMetric("Api.Requests.Duration", elapsedMs, properties);
        logger.LogDebug("Successfully tracked duration metric: {ElapsedMs}ms", elapsedMs);

        metricsService.TrackMetric($"Api.StatusCode.{statusCode}", 1, properties);
        logger.LogDebug("Successfully tracked status code metric: {StatusCode}", statusCode);
    }

    private async Task LogRequestDataIfEnabled(HttpContext context)
    {
        if (!logger.IsEnabled(LogLevel.Debug))
            return;

        var method = context.Request.Method;
        var path = context.Request.Path.Value ?? "unknown";
        var requestQuery = context.Request.QueryString.ToString();

        logger.LogDebug("Logging request data for {Method} {Path}", method, path);

        if (!string.IsNullOrEmpty(requestQuery))
        {
            logger.LogDebug("Request query: {Query}", requestQuery);
        }

        if (ShouldLogRequestBody(context))
        {
            context.Request.EnableBuffering();

            var requestBody = await ReadRequestBodyAsync(context);

            if (!string.IsNullOrEmpty(requestBody))
            {
                logger.LogDebug("Request body: {Body}", requestBody);
            }
        }
    }

    private static bool ShouldLogRequestBody(HttpContext context)
    {
        // Only log body for methods that typically have bodies  
        var method = context.Request.Method;
        if (!string.Equals(method, "GET", StringComparison.OrdinalIgnoreCase)
            && !string.Equals(method, "POST", StringComparison.OrdinalIgnoreCase)
            && !string.Equals(method, "PUT", StringComparison.OrdinalIgnoreCase)
            && !string.Equals(method, "PATCH", StringComparison.OrdinalIgnoreCase))
        {
            return false;
        }

        // Skip certain content types  
        var contentType = context.Request.ContentType;
        if (contentType != null && (contentType.StartsWith("multipart/", StringComparison.OrdinalIgnoreCase) ||
                                    contentType.StartsWith("application/octet-stream", StringComparison.OrdinalIgnoreCase)))
        {
            return false;
        }

        return true;
    }

    private static async Task<string> ReadRequestBodyAsync(HttpContext context)
    {
        using var reader = new StreamReader(
            context.Request.Body,
            encoding: Encoding.UTF8,
            detectEncodingFromByteOrderMarks: false,
            leaveOpen: true);

        var body = await reader.ReadToEndAsync();

        // Reset position for next middleware
        context.Request.Body.Position = 0;

        return body;
    }
}