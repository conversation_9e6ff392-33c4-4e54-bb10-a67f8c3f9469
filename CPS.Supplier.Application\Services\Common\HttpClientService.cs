using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Exceptions;
using CPS.Supplier.Application.Models.AdministrationApi;
using CPS.Supplier.Application.Services.Auth0.Contracts;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Application.Services.Common.Dto;
using CPS.Supplier.Application.Services.Localization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CPS.Supplier.Application.Services.Common;

public class HttpClientService : IHttpClientService
{
    private readonly HttpClient _httpClient;
    private readonly IAuth0TokenService _auth0TokenService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<HttpClientService> _logger;
    private readonly AdministrationApiSettings _settings;
    private readonly ILocalizationService _localizationService;

    public HttpClientService(HttpClient httpClient,
        IAuth0TokenService auth0TokenService,
        IHttpContextAccessor httpContextAccessor,
        ILogger<HttpClientService> logger,
        IOptions<AdministrationApiSettings> settings,
        ILocalizationService localizationService)
    {
        _httpClient = httpClient;
        _auth0TokenService = auth0TokenService;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
        _settings = settings.Value ?? throw new NotFoundException("AdministrationApi settings configuration is missing");
        _localizationService = localizationService;

        if (string.IsNullOrEmpty(_settings.BaseUrl))
        {
            throw new NotFoundException("AdministrationApi:BaseUrl configuration is missing");
        }
    }

    public async Task<TResponse?> GetAsync<TResponse>(string endpoint, CancellationToken cancellationToken, bool allowAnonymous = false)
    {
        var fullUrl = BuildFullUrl(endpoint);
        _logger.LogDebug("Making GET request: {Url}", fullUrl);

        using var request = new HttpRequestMessage(HttpMethod.Get, fullUrl);

        if (allowAnonymous)
        {
            var response = await _httpClient.GetAsync(new Uri(fullUrl), cancellationToken);
            return await ProcessHttpResponseAsync<TResponse>(response, fullUrl, "GET", cancellationToken);
        }
        else
        {
            await SetupHttpClientAsync();
            var response = await _httpClient.GetAsync(new Uri(fullUrl), cancellationToken);
            return await ProcessHttpResponseAsync<TResponse>(response, fullUrl, "GET", cancellationToken);
        }
    }

    public async Task<TResponse?> PostAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken, bool allowAnonymous = false)
       where TRequest : class
    {
        return await SendWithBodyAsync<TRequest, TResponse>("POST", endpoint, request,
            (uri, content, token) => _httpClient.PostAsync(uri, content, token), cancellationToken, allowAnonymous);
    }

    public async Task<TResponse?> PutAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken, bool allowAnonymous = false)
        where TRequest : class
    {
        return await SendWithBodyAsync<TRequest, TResponse>("PUT", endpoint, request,
            (uri, content, token) => _httpClient.PutAsync(uri, content, token), cancellationToken, allowAnonymous);
    }

    private async Task<TResponse?> SendWithBodyAsync<TRequest, TResponse>(
        string httpMethod,
        string endpoint,
        TRequest request,
        Func<Uri, HttpContent, CancellationToken, Task<HttpResponseMessage>> httpCall,
        CancellationToken cancellationToken,
        bool allowAnonymous = false)
        where TRequest : class
    {
        if (!allowAnonymous)
        {
            await SetupHttpClientAsync();
        }

        var fullUrl = BuildFullUrl(endpoint);
        var jsonContent = JsonSerializer.Serialize(request, GetJsonSerializerOptions());

        using var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

        _logger.LogDebug("Making {HttpMethod} request: {Url}", httpMethod, fullUrl);

        var response = await httpCall(new Uri(fullUrl), content, cancellationToken);

        return await ProcessHttpResponseAsync<TResponse>(response, fullUrl, httpMethod, cancellationToken);
    }

    private async Task<TResponse?> ProcessHttpResponseAsync<TResponse>(
        HttpResponseMessage response,
        string fullUrl,
        string httpMethod,
        CancellationToken cancellationToken)
    {
        if (!response.IsSuccessStatusCode)
        {
            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogError("{HttpMethod} request failed. Status: {StatusCode}, Content: {Content}, Url: {Url}",
                httpMethod, response.StatusCode, errorContent, fullUrl);

            // Parse Admin API error response and throw appropriate exception
            var adminError = TryParseAdminApiError(errorContent);
            ThrowAppropriateException(response.StatusCode, adminError);
        }

        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

        if (string.IsNullOrEmpty(responseContent))
        {
            _logger.LogWarning("{HttpMethod} request returned empty response. Url: {Url}", httpMethod, fullUrl);
            return default;
        }

        if (typeof(TResponse) == typeof(bool))
        {
            if (bool.TryParse(responseContent, out var result))
            {
                return (TResponse)(object)result;
            }
            return default;
        }

        var resultObj = JsonSerializer.Deserialize<TResponse>(responseContent, GetJsonSerializerOptions());
        _logger.LogDebug("{HttpMethod} request completed successfully. Url: {Url}", httpMethod, fullUrl);

        return resultObj;
    }

    private async Task SetupHttpClientAsync()
    {
        var accessToken = await _auth0TokenService.GetAccessTokenAsync();

        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        var currentUserId = GetCurrentUserId();

        if (!string.IsNullOrEmpty(currentUserId))
        {
            _httpClient.DefaultRequestHeaders.Remove("X-Requested-By-User");
            _httpClient.DefaultRequestHeaders.Add("X-Requested-By-User", currentUserId);
        }
    }

    private string BuildFullUrl(string endpoint)
    {
        var cleanEndpoint = endpoint.StartsWith('/') ? endpoint[1..] : endpoint;
        return $"{_settings.BaseUrl.TrimEnd('/')}/{cleanEndpoint}";
    }

    private string GetCurrentUserId()
    {
        var user = _httpContextAccessor.HttpContext?.User;
        if (user?.Identity?.IsAuthenticated == true)
        {
            var username = user.FindFirst(CustomClaimTypes.UserId)?.Value;

            return username ?? string.Empty;
        }

        return string.Empty;
    }

    private static JsonSerializerOptions GetJsonSerializerOptions()
    {
        return new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
        };
    }

    private static ApiErrorDto? TryParseAdminApiError(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return null;

        try
        {
            return JsonSerializer.Deserialize<ApiErrorDto>(content, GetJsonSerializerOptions());
        }
        catch (JsonException)
        {
            return null; // Not a structured error response
        }
        catch (ArgumentNullException)
        {
            return null; // Invalid content
        }
        catch (NotSupportedException)
        {
            return null; // Unsupported content type
        }
    }

    private void ThrowAppropriateException(HttpStatusCode statusCode, ApiErrorDto? adminError)
    {
        switch (statusCode)
        {
            case HttpStatusCode.BadRequest when adminError?.Errors?.Any() == true:
                throw new ValidationException(JsonSerializer.Serialize(adminError.Errors));

            case HttpStatusCode.BadRequest:
                throw new BadRequestException(adminError?.Message ?? _localizationService.GetString(ErrorMessages.RequestFailed));

            case HttpStatusCode.NotFound:
                throw new NotFoundException(adminError?.Message ?? _localizationService.GetString(ErrorMessages.ResourceNotFound));

            case HttpStatusCode.Forbidden:
                throw new ForbiddenException(adminError?.Message ?? _localizationService.GetString(ErrorMessages.AccessForbidden));

            case HttpStatusCode.Unauthorized:
                throw new UnauthorizedException(adminError?.Message ?? _localizationService.GetString(ErrorMessages.Unauthorized));

            default:
                throw new BadRequestException(adminError?.Message ?? $"{_localizationService.GetString(ErrorMessages.RequestFailed)}: {statusCode}");
        }
    }
}