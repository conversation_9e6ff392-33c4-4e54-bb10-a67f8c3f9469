﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>

namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SearchLogConfiguration : IEntityTypeConfiguration<SearchLog>
    {
        public void Configure(EntityTypeBuilder<SearchLog> entity)
        {
            entity
                .HasNoKey()
                .ToTable("SEarchLog", "SPL");

            entity.Property(e => e.IntCompany).HasColumnName("intCompany");
            entity.Property(e => e.IntUserSk)
                .HasMaxLength(150)
                .IsUnicode(false)
                .HasColumnName("intUserSk");
            entity.Property(e => e.StrCity)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("strCity");
            entity.Property(e => e.StrPostCode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("strPostCode");
            entity.Property(e => e.StrProvince)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("strProvince");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SearchLog> entity);
    }
}
