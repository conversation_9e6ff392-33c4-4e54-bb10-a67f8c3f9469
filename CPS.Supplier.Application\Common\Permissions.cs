namespace CPS.Supplier.Application.Common;

/// <summary>
/// Permission names within modules.
/// Extracted from AuthorizationConstants for Microsoft .NET 8 standards compliance.
/// </summary>
public static class Permissions
{
    // User Management Permissions
    public const string ViewUser = "ViewUser";
    public const string CreateUser = "CreateUser";
    public const string EditUser = "EditUser";

    // File Operation Permissions (matching database seed script)
    public const string MapUnmapExternalMappingFiles = "MapUnmapExternalMappingFiles";
    public const string MapUnmapAramarkReviewOrRejectedFiles = "MapUnmapAramarkReviewRejectedFiles";
    public const string MapUnmapAramarkAuditFiles = "MapUnmapAramarkAuditFiles";
    public const string MarkAramarkReviewOrRejectedRecordValidInvalid = "MarkAramarkReviewRejectedFilesRecordValidInvalid";
    public const string MarkAramarkAuditRecordValidInvalid = "MarkAramarkAuditFilesRecordValidInvalid";
    public const string ApproveRejectAramarkAuditFiles = "ApproveRejectAramarkAuditFiles";
    public const string DeleteExternalMappingFiles = "DeleteExternalMappingFiles";
    public const string DeleteAramarkReviewOrRejectedFiles = "DeleteAramarkReviewRejectedFiles";
    public const string DeleteAramarkAuditFiles = "DeleteAramarkAuditFiles";
    public const string DeleteApprovedFiles = "DeleteApprovedFiles";
    public const string UploadFile = "UploadFile";
}