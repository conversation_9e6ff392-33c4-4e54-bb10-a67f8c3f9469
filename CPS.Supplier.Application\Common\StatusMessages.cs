namespace CPS.Supplier.Application.Common;

/// <summary>
/// Resource keys for status messages with performance-optimized mapping.
/// Extracted from LocalizationConstants for Microsoft .NET 8 standards compliance.
/// </summary>
public static class StatusMessages
{
    // Status Labels (for UI-only statuses)
    public const string ProcessingLabel = "StatusLabel_Processing";

    // Status Messages (for detailed descriptions)
    public const string Uploaded = "StatusMessages_Uploaded";
    public const string Processing = "StatusMessages_Processing";
    public const string ExternalMapping = "StatusMessages_ExternalMapping";
    public const string Approved = "StatusMessages_Approved";
    public const string Rejected = "StatusMessages_Rejected";
    public const string Completed = "StatusMessages_Completed";
    public const string Unknown = "StatusMessages_Unknown";
    public const string NotFound = "StatusMessages_NotFound";

    // Static mapping for better performance
    private static readonly Dictionary<string, string> StatusMapping =
        new(StringComparer.OrdinalIgnoreCase)
        {
            ["uploaded"] = Uploaded,
            ["processing"] = Processing,
            ["external mapping"] = ExternalMapping,
            ["approved"] = Approved,
            ["rejected"] = Rejected,
            ["completed"] = Completed
        };

    public static string GetStatusMessage(string status)
    {
        return StatusMapping.GetValueOrDefault(status, Unknown);
    }
}