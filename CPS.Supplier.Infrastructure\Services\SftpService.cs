using CPS.Supplier.Application.Common.Configuration;
using Renci.SshNet;

namespace CPS.Supplier.Infrastructure.Services;

public class SftpService(IOptions<SftpSettings> sftpSettings, ILogger<SftpService> logger) : ISftpService
{
    private readonly SftpSettings _settings = sftpSettings.Value;

    private SftpClient CreateSftpClient()
    {
        var client = new SftpClient(_settings.Host, _settings.Port, _settings.Username, _settings.Password);
        client.Connect();
        return client;
    }

    public void UploadFile(string localFilePath, string remoteFileName)
    {
        logger.LogDebug("Uploading file to SFTP: {LocalFilePath} -> {RemoteFileName}", localFilePath, remoteFileName);

        using var client = CreateSftpClient();
        var remotePath = $"{_settings.BasePath.TrimEnd('/')}/{remoteFileName}";

        EnsureDirectoryExists(client, Path.GetDirectoryName(remotePath));

        using var fileStream = File.OpenRead(localFilePath);
        client.UploadFile(fileStream, remotePath, true);

        logger.LogInformation("Successfully uploaded file to SFTP: {LocalFilePath} -> {RemotePath}", localFilePath, remotePath);
    }

    public void DownloadFile(string remoteFileName, string localFilePath)
    {
        using var client = CreateSftpClient();
        var remotePath = $"{_settings.BasePath.TrimEnd('/')}/{remoteFileName}";

        var localDirectory = Path.GetDirectoryName(localFilePath);
        if (!string.IsNullOrEmpty(localDirectory) && !Directory.Exists(localDirectory))
        {
            Directory.CreateDirectory(localDirectory);
        }

        using var fileStream = File.Create(localFilePath);
        client.DownloadFile(remotePath, fileStream);

        logger.LogInformation("Successfully downloaded file from SFTP: {RemotePath} -> {LocalFilePath}", remotePath, localFilePath);
    }

    private static void EnsureDirectoryExists(SftpClient client, string? directoryPath)
    {
        if (string.IsNullOrEmpty(directoryPath)) return;

        var parts = directoryPath.Split('/', StringSplitOptions.RemoveEmptyEntries);
        var currentPath = "/";

        foreach (var part in parts)
        {
            currentPath = $"{currentPath.TrimEnd('/')}/{part}";
            if (!client.Exists(currentPath))
            {
                client.CreateDirectory(currentPath);
            }
        }
    }
}