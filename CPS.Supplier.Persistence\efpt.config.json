﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "SPLDBContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "CPS.Supplier.Persistence",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[SPL].[ActivityTracking]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[ActivityType]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[ETLColumnConfig]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[ETLConfig]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[FilelistLog]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[MonthNames]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[output]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SEarchLog]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SPLScreens]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SSIS_EventRecord]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[Supplier_Customer_Mapping]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[Supplier_Customer_Mapping_History]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SupplierContactUs]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SupplierVolumeDataStatus]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SupplierVolumeErrorTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SupplierVolumeFile]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SupplierVolumeFileData]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SupplierVolumeFileData_Staging]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SupplierVolumeFileErrors]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SupplierVolumeFileMonth]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[SupplierVolumeFileStatus]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[templog]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[test]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[UploadFile_log]",
         "ObjectType": 0
      },
      {
         "Name": "[SPL].[CPSCustomer]",
         "ObjectType": 3
      },
      {
         "Name": "[SPL].[StateProvince]",
         "ObjectType": 3
      },
      {
         "Name": "[SPL].[Supplier]",
         "ObjectType": 3
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": true,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}