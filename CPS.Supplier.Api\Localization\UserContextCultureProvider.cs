using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.UserContext.Contracts;
using Microsoft.AspNetCore.Localization;

namespace CPS.Supplier.Api.Localization;

public class UserContextCultureProvider : RequestCultureProvider
{
    public override Task<ProviderCultureResult?> DetermineProviderCultureResult(HttpContext httpContext)
    {
        try
        {
            var currentUserService = httpContext.RequestServices.GetService<ICurrentUserService>();
            if (currentUserService != null)
            {
                var languageId = currentUserService.LanguageId;
                var culture = languageId == Languages.French ? "fr-CA" : "en-US";
                return Task.FromResult<ProviderCultureResult?>(new ProviderCultureResult(culture));
            }
        }
        catch (InvalidOperationException)
        {
            // User context not available yet (before authentication)
            // Fall back to default culture
        }

        // Default to English when user context unavailable
        return Task.FromResult<ProviderCultureResult?>(new ProviderCultureResult("en-US"));
    }
}