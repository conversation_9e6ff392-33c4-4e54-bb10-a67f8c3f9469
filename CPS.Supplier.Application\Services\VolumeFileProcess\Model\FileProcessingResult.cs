﻿namespace CPS.Supplier.Application.Services.VolumeFileProcess.Model;
public class FileProcessingResult
{
    public bool Success { get; set; }
    public int DataFileId { get; set; }
    public string? ErrorMessage { get; set; }
    public IEnumerable<ValidationError> ValidationErrors { get; set; } = [];
    public string ProcessingStatus { get; set; } = string.Empty;
    public string CorrelationId { get; set; } = string.Empty;
}