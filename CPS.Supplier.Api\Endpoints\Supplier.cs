using CPS.Supplier.Api.Infrastructure;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.Supplier.Contracts;
using Microsoft.AspNetCore.Authorization;

namespace CPS.Supplier.Api.Endpoints
{
    public class Supplier : IEndpointGroupBase
    {
        public void Map(WebApplication app)
        {
            var group = app.MapGroup(this);
            group.RequireAuthorization();
            group.MapGet(GetSuppliersWithUserCount, "/with-user-count");
            group.MapGet(GetSupplierUsers, "/{supplierId:int}/users");
        }

        private static async Task<IResult> GetSuppliersWithUserCount(ISupplierService supplierService, IMetricsService metricsService, CancellationToken ct)
        {
            metricsService.TrackMetric("UserApi.with-user-count.Count", 1);
            var results = await supplierService.GetSuppliersWithUserCount(ct);
            return TypedResults.Ok(results);
        }

        [Authorize(Policy = Policies.ViewUserOrManage)]
        private static async Task<IResult> GetSupplierUsers(ISupplierService supplierService, int supplierId, IMetricsService metricsService, CancellationToken ct)
        {
            metricsService.TrackMetric("SupplierApi.users.Count", 1);
            var results = await supplierService.GetSupplierUsers(supplierId, ct);
            return TypedResults.Ok(results);
        }
    }
}