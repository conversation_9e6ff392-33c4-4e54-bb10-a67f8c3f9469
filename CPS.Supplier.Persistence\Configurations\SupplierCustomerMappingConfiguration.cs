﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
namespace CPS.Supplier.Persistence.Configurations
{
    public partial class SupplierCustomerMappingConfiguration : IEntityTypeConfiguration<SupplierCustomerMapping>
    {
        public void Configure(EntityTypeBuilder<SupplierCustomerMapping> entity)
        {
            entity.HasKey(e => e.IntSupplierCustomerMappingId)
                .HasName("PK_SupplierCustomerMapping")
                .IsClustered(false);

            entity.ToTable("Supplier_Customer_Mapping", "SPL");

            entity.Property(e => e.IntSupplierCustomerMappingId).HasColumnName("intSupplierCustomerMappingID");
            entity.Property(e => e.IntDataStatusId).HasColumnName("intDataStatusID");
            entity.Property(e => e.IntSupplierId).HasColumnName("intSupplierID");
            entity.Property(e => e.IntUserSk).HasColumnName("intUserSK");
            entity.Property(e => e.Str<PERSON>pscustomerId)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("strARACPSCustomerID");
            entity.Property(e => e.StrSupplierCustomerId)
                .IsRequired()
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("strSupplierCustomerID");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<SupplierCustomerMapping> entity);
    }
}
