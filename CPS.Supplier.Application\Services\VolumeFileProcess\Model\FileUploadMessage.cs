using System.Text.Json.Serialization;

namespace CPS.Supplier.Application.Services.VolumeFileProcess.Model;

public class FileUploadMessage
{
    [JsonPropertyName("fileName")]
    public string FileName { get; set; } = string.Empty;

    [JsonPropertyName("blobPath")]
    public string BlobPath { get; set; } = string.Empty;

    [JsonPropertyName("containerName")]
    public string ContainerName { get; set; } = string.Empty;

    [JsonPropertyName("processingMonth")]
    public string ProcessingMonth { get; set; } = string.Empty;

    [JsonPropertyName("userSK")]
    public int UserSK { get; set; }

    [JsonPropertyName("supplierId")]
    public int SupplierId { get; set; }

    [JsonPropertyName("correlationId")]
    public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
}