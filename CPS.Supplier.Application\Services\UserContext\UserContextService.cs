using CPS.Supplier.Application.Services.UserContext.Contracts;
using CPS.Supplier.Application.Services.UserContext.Dto;
using CPS.Supplier.Domain.Entities.Models;
using Microsoft.Extensions.Logging;

namespace CPS.Supplier.Application.Services.UserContext;

public class UserContextService : IUserContextService
{
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<UserContextService> _logger;

    public UserContextService(
        IApplicationDbContext dbContext,
        ILogger<UserContextService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<UserContextDto?> GetUserContext(int userId)
    {
        var userContextRecords = await GetUserContextFromView(userId);

        if (userContextRecords.Count == 0)
        {
            _logger.LogWarning("No user context found for userId: {UserId}", userId);
            return null;
        }

        var userContext = await BuildUserContextAsync(userContextRecords);

        return userContext;
    }

    private async Task<List<VwUserContext>> GetUserContextFromView(int userId)
    {
        return await _dbContext.GetDbSet<VwUserContext>()
            .Where(u => u.UserId == userId)
            .ToListAsync();
    }

    private async Task<UserContextDto> BuildUserContextAsync(List<VwUserContext> records)
    {
        var firstRecord = records[0];
        var userContext = new UserContextDto
        {
            UserId = firstRecord.UserId,
            Username = firstRecord.Username,
            SupplierId = firstRecord.SupplierId,
            FacilityId = firstRecord.FacilityId,
            LanguageId = firstRecord.DefaultLanguageId,
            IsSuperAdmin = firstRecord.IsSuperAdmin,
        };

        // Get supplier name if supplier ID is present
        if (firstRecord.SupplierId.HasValue)
        {
            var supplier = await _dbContext.GetDbSet<Domain.Entities.Supplier>()
                .Where(s => s.IntSupplierId == firstRecord.SupplierId.Value)
                .Select(s => s.StrName)
                .FirstOrDefaultAsync();

            userContext.SupplierName = supplier;
        }

        PopulateCollections(userContext, records);

        return userContext;
    }

    private static void PopulateCollections(UserContextDto userContext, List<VwUserContext> records)
    {
        // Extract unique roles
        var roles = records
            .Where(r => !string.IsNullOrEmpty(r.RoleName))
            .Select(r => r.RoleName)
            .Distinct(StringComparer.Ordinal);

        // Add roles to collection
        foreach (var role in roles)
        {
            userContext.Roles.Add(role);
        }

        // If super admin, skip module permissions as they're not needed
        if (userContext.IsSuperAdmin)
        {
            return;
        }

        // Group permissions by module ID
        var modulePermissions = records
            .Where(r => !string.IsNullOrEmpty(r.ModuleName) && !string.IsNullOrEmpty(r.PermissionKey))
            .GroupBy(r => r.ModuleName!.Replace(" ", "", StringComparison.Ordinal), StringComparer.OrdinalIgnoreCase)
            .ToDictionary(
                g => g.Key,
                g => g.Select(r => r.PermissionKey!)
                      .Distinct(StringComparer.Ordinal)
                      .ToList()
            , StringComparer.OrdinalIgnoreCase);

        // Add module permissions
        foreach (var modulePermission in modulePermissions)
        {
            userContext.ModulePermissions[modulePermission.Key] = modulePermission.Value;
        }
    }
}