using System.Diagnostics.CodeAnalysis;
using CPS.Supplier.Application.Common;
using CPS.Supplier.Application.Services.DataFile.Commands;
using CPS.Supplier.Application.Services.Localization;
using Microsoft.AspNetCore.Http;

namespace CPS.Supplier.Application.Services.DataFile.Validators;

public class UploadFileCommandValidator : AbstractValidator<UploadFileCommand>
{
    [SuppressMessage("CodeQuality", "S1450:Remove the field '_localizationService' and declare it as a local variable in the relevant methods.", Justification = "Required for FluentValidation rule setup in constructor")]
    private readonly ILocalizationService _localizationService;

    public UploadFileCommandValidator(ILocalizationService localizationService)
    {
        _localizationService = localizationService;

        RuleFor(x => x.File)
            .NotNull()
            .WithMessage(_localizationService.GetString(ValidationMessages.FileRequired))
            .Must(BeValidFileSize)
            .WithMessage(_localizationService.GetString(ValidationMessages.InvalidFileSize))
            .Must(BeValidFileExtension)
            .WithMessage(_localizationService.GetString(ValidationMessages.InvalidFileExtension))
            .Must(BeValidFileName)
            .WithMessage(_localizationService.GetString(ValidationMessages.FileNameTooLong))
            .MustAsync(BeValidCsvFormatAsync)
            .WithMessage(_localizationService.GetString(ValidationMessages.InvalidCsvFormat));

        RuleFor(x => x.SupplierId)
            .Must(BeValidSupplierId)
            .WithMessage(_localizationService.GetString(ValidationMessages.ValidSupplierIdRequired));
    }

    private static bool BeValidFileSize(IFormFile? file)
    {
        if (file == null) return false;

        var maxSizeBytes = FileRules.MaxFileUploadSizeMb * 1024 * 1024;
        return file.Length > 0 && file.Length <= maxSizeBytes;
    }

    private static bool BeValidFileExtension(IFormFile? file)
    {
        if (file == null) return false;

        var allowedExtensions = new[] { ".csv" }; // Only CSV files allowed
        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        return allowedExtensions.Contains(extension, StringComparer.OrdinalIgnoreCase);
    }

    private static bool BeValidFileName(IFormFile? file)
    {
        if (file == null) return false;

        return file.FileName.Length <= FileRules.MaxFileNameLength;
    }

    private static bool BeValidSupplierId(int? supplierId)
    {
        // SupplierId is optional - will be determined from user context if not provided
        return !supplierId.HasValue || supplierId.Value > 0;
    }

    private static async Task<bool> BeValidCsvFormatAsync(IFormFile? file, CancellationToken cancellationToken)
    {
        if (file == null) return false;

        try
        {
            using var stream = file.OpenReadStream();
            using var reader = new StreamReader(stream);
            var header = await reader.ReadLineAsync(cancellationToken);

            // Must have either commas or semicolons as separators
            return !string.IsNullOrEmpty(header) &&
                   (header.Contains(',', StringComparison.Ordinal) || header.Contains(';', StringComparison.Ordinal));
        }
        catch (IOException)
        {
            return false;
        }
        catch (UnauthorizedAccessException)
        {
            return false;
        }
    }
}