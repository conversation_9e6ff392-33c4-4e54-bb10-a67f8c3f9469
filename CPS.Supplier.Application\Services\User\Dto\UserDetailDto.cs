﻿namespace CPS.Supplier.Application.Services.User.Dto;

public class UserDetailDto
{
    public int UserId { get; set; }
    public string Username { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = null!;
    public string AdminNotes { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Language { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;
    public int LanguageId { get; set; }
    public DateTime? CreatedDate { get; set; } = null!;
    public string? CreatedBy { get; set; } = null!;
    public DateTime? LastUpdatedDate { get; set; } = null!;
    public string? LastUpdatedBy { get; set; } = null!;
    public DisplayContactDetailDto ContactDetails { get; init; } = new();
    public IReadOnlyCollection<string> Roles { get; set; } = [];
    public IReadOnlyCollection<int> RoleIds { get; init; } = [];
}

public class DisplayContactDetailDto
{
    public string Email { get; set; } = string.Empty;
    public IReadOnlyCollection<DisplayPhoneDetailDto> PhoneNumberDetails { get; init; } = Array.Empty<DisplayPhoneDetailDto>();
    public IReadOnlyCollection<DisplayAddressDetailDto> AddressDetails { get; init; } = Array.Empty<DisplayAddressDetailDto>();
}

public class DisplayPhoneDetailDto
{
    public int PhoneNumberTypeId { get; set; }
    public string? PhoneType { get; set; }
    public string? PhoneNumber { get; set; }
    public int? CountryCodeId { get; set; }
    public string? CountryCode { get; set; }
}

public class DisplayAddressDetailDto
{
    public int CountryId { get; set; }
    public int AddressTypeId { get; set; }
    public int StateProvinceId { get; set; }
    public string? AddressType { get; set; }
    public string? AddressLine1 { get; set; }
    public string? AddressLine2 { get; set; }
    public string? City { get; set; }
    public string? Province { get; set; }
    public string? PostalCode { get; set; }
    public string? Country { get; set; }
}
public class UpdateUserStatusCommand
{
    public bool IsActive { get; set; }
}