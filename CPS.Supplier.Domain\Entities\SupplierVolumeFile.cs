﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable

namespace CPS.Supplier.Domain.Entities;

public partial class SupplierVolumeFile
{
    public int IntDataFileId { get; set; }

    public string StrFileName { get; set; }

    public string StrMonth { get; set; }

    public string StrYear { get; set; }

    public int IntTotalRecords { get; set; }

    public int IntUnmappedRecords { get; set; }

    public decimal DecTotalVolume { get; set; }

    public int IntFileStatusId { get; set; }

    public int IntUserSk { get; set; }

    public int? BlnHasMultipleMonths { get; set; }

    public int? IntSupplierId { get; set; }

    public DateTime? DttmUploadDate { get; set; }

    public virtual SupplierVolumeFileStatus IntFileStatus { get; set; }

    public virtual ICollection<SupplierVolumeFileError> SupplierVolumeFileErrors { get; set; } = new List<SupplierVolumeFileError>();

    public virtual ICollection<SupplierVolumeFileMonth> SupplierVolumeFileMonths { get; set; } = new List<SupplierVolumeFileMonth>();
}
