namespace CPS.Supplier.Application.Services.User.Commands;
public class UpsertUserCommand
{
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string UserName { get; set; } = null!;
    public string Password { get; set; } = null!;
    public int LanguageId { get; set; }
    public int? SupplierId { get; set; }
    public string? Position { get; set; }
    public bool Status { get; set; }
    public string? AdminNotes { get; set; }
    public IReadOnlyCollection<int> RoleIds { get; init; } = [];
    public ContactDetailCommand Contacts { get; set; } = new();
}

public class ContactDetailCommand
{
    public string Email { get; set; } = null!;
    public IReadOnlyCollection<PhoneNumberDetailCommand> PhoneNumber { get; init; } = [];
    public IReadOnlyCollection<AddressDetailCommand> Address { get; init; } = [];
}

public class PhoneNumberDetailCommand
{
    public string Number { get; set; } = null!;
    public int PhoneNumberTypeId { get; set; }
    public int? CountryCodeId { get; set; }
}

public class AddressDetailCommand
{
    public string AddressLine1 { get; set; } = null!;
    public string? AddressLine2 { get; set; }
    public string City { get; set; } = null!;
    public string PostalCode { get; set; } = null!;
    public int AddressTypeId { get; set; }
    public int StateProvinceId { get; set; }
}