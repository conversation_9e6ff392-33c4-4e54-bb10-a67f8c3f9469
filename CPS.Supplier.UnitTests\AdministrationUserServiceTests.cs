﻿using CPS.Supplier.Application.Exceptions;
using CPS.Supplier.Application.Interfaces.Persistence;
using CPS.Supplier.Application.Models.AdministrationApi;
using CPS.Supplier.Application.Services.Common.Contracts;
using CPS.Supplier.Application.Services.Localization;
using CPS.Supplier.Application.Services.User;
using CPS.Supplier.Application.Services.User.Commands;
using CPS.Supplier.Application.Services.User.Dto;
using CPS.Supplier.Application.Services.UserContext.Contracts;
using CPS.Supplier.Domain.Entities;
using CPS.Supplier.UnitTests.MockDatabase;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute.ExceptionExtensions;

namespace CPS.Supplier.UnitTests;
public class AdministrationUserServiceTests
{
    private readonly IHttpClientService _httpClientService;
    private readonly IOptions<AdministrationApiSettings> _settings;
    private readonly ILogger<UserService> _logger;
    private readonly ILocalizationService _localizationService;
    private readonly UserService _service;
    private readonly AdministrationApiSettings _apiSettings;
    private readonly ICurrentUserService _currentUserService;
    private readonly IApplicationDbContext _dbContext;

    public AdministrationUserServiceTests()
    {
        // Setup mocks
        _httpClientService = Substitute.For<IHttpClientService>();
        _logger = Substitute.For<ILogger<UserService>>();
        _localizationService = Substitute.For<ILocalizationService>();
        _dbContext = Substitute.For<IApplicationDbContext>();

        // Setup configuration
        _apiSettings = new AdministrationApiSettings
        {
            User = new UserSettings
            {
                GetUserDetailId = "/api/user/{userId}/detail",
                CreateSupplierUser = "/api/user/create-supplier-user",
                GetEditUser = "/api/user/{userId}/edit",
                UpdateUser = "/api/user/{userId}/update",
                UpdateSupplierUserStatus = "/api/user/{userId}/update-supplier-user-status",
                GetPasswordPolicy = "/api/user/password-policy",
                EmailExists = "/api/user/email-exists",
                UsernameExists = "/api/user/username-exists"
            }
        };

        _settings = Substitute.For<IOptions<AdministrationApiSettings>>();
        _settings.Value.Returns(_apiSettings);

        _currentUserService = Substitute.For<ICurrentUserService>();

        // Create the actual service (not a mock)
        _service = new UserService(_httpClientService, _settings, _currentUserService, _localizationService, _logger);
    }

    #region GetUserByIdAsync Tests

    [Fact]
    public async Task GetUserByIdAsyncShouldReturnUserWhenSuccessful()
    {
        // Arrange
        var userId = 123;
        var expectedUser = new UserDetailDto
        {
            UserId = userId,
            Username = "testuser",
            FirstName = "John",
            LastName = "Doe",
        };

        var expectedEndpoint = "/api/user/123/detail";
        _httpClientService.GetAsync<UserDetailDto>(expectedEndpoint, Arg.Any<CancellationToken>())
            .Returns(expectedUser);

        // Act
        var result = await _service.GetUserByIdAsync(userId);

        // Assert
        result.ShouldNotBeNull();
        result.UserId.ShouldBe(userId);
        result.Username.ShouldBe("testuser");
        result.FirstName.ShouldBe("John");
        result.LastName.ShouldBe("Doe");

        // Verify the correct endpoint was called
        await _httpClientService.Received(1).GetAsync<UserDetailDto>(expectedEndpoint, Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task GetUserByIdAsyncShouldReturnNullWhenUserNotFound()
    {
        // Arrange
        var userId = 999;
        var expectedEndpoint = "/api/user/999/detail";
        _httpClientService.GetAsync<UserDetailDto>(expectedEndpoint, Arg.Any<CancellationToken>())
            .Returns((UserDetailDto?)null);

        // Act
        var result = await _service.GetUserByIdAsync(userId);

        // Assert
        result.ShouldBeNull();
        await _httpClientService.Received(1).GetAsync<UserDetailDto>(expectedEndpoint, Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task GetUserByIdAsyncShouldRethrowExceptionWhenHttpClientThrows()
    {
        // Arrange
        var userId = 123;
        var expectedEndpoint = "/api/user/123/detail";
        var expectedException = new HttpRequestException("Network error");

        _httpClientService.GetAsync<UserDetailDto>(expectedEndpoint, Arg.Any<CancellationToken>())
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Should.ThrowAsync<HttpRequestException>(
            () => _service.GetUserByIdAsync(userId));

        exception.ShouldBe(expectedException);
    }

    [Fact]
    public async Task GetUserByIdAsyncShouldPassCancellationTokenWhenProvided()
    {
        // Arrange
        var userId = 123;
        var cancellationToken = new CancellationToken();
        var expectedEndpoint = "/api/user/123/detail";

        _httpClientService.GetAsync<UserDetailDto>(expectedEndpoint, cancellationToken)
            .Returns(new UserDetailDto());

        // Act
        await _service.GetUserByIdAsync(userId, cancellationToken);

        // Assert
        await _httpClientService.Received(1).GetAsync<UserDetailDto>(expectedEndpoint, cancellationToken);
    }

    #endregion

    #region CreateSupplierUser Tests

    [Fact]
    public async Task CreateSupplierUserShouldReturnUserIdWhenSuccessful()
    {
        // Arrange
        var command = new UpsertUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            UserName = "johndoe",
            Password = "password123",
            LanguageId = 1,
            Status = true,
            Contacts = new ContactDetailCommand
            {
                Email = "<EMAIL>"
            }
        };

        var expectedUserId = 456;
        var expectedEndpoint = "/api/user/create-supplier-user";

        // Mock DbSet to return no users (so username/email does not exist)
        var dbSet = CreateMockDbSet<VwSupplierUser>(new List<VwSupplierUser>());
        _dbContext.GetDbSet<VwSupplierUser>().Returns(dbSet);

        _httpClientService.PostAsync<UpsertUserCommand, int>(expectedEndpoint, command, Arg.Any<CancellationToken>())
            .Returns(expectedUserId);

        // Act
        var result = await _service.CreateUser(command);

        // Assert
        result.ShouldBe(expectedUserId);

        // Verify the correct endpoint was called
        await _httpClientService.Received(1).PostAsync<UpsertUserCommand, int>(expectedEndpoint, command, Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task CreateSupplierUserShouldHandleStringResponseCorrectly()
    {
        // Arrange
        var command = new UpsertUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            UserName = "johndoe",
            Password = "password123",
            LanguageId = 1,
            Status = true,
        };

        var expectedUserId = 789;
        var expectedEndpoint = "/api/user/create-supplier-user";

        // Mock DbSet to return no users (so username/email does not exist)
        var dbSet = CreateMockDbSet<VwSupplierUser>(new List<VwSupplierUser>());
        _dbContext.GetDbSet<VwSupplierUser>().Returns(dbSet);

        _httpClientService.PostAsync<UpsertUserCommand, int>(expectedEndpoint, command, Arg.Any<CancellationToken>())
            .Returns(expectedUserId);

        // Act
        var result = await _service.CreateUser(command);

        // Assert
        result.ShouldBe(expectedUserId);
    }

    [Fact]
    public async Task CreateSupplierUserShouldThrowExceptionWhenResultIsNull()
    {
        // Arrange
        var command = new UpsertUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            UserName = "johndoe",
            Password = "password123",
            LanguageId = 1,
            Status = true,
        };

        var expectedEndpoint = "/api/user/create-supplier-user";

        // Mock DbSet to return no users (so username/email does not exist)
        var dbSet = CreateMockDbSet<VwSupplierUser>(new List<VwSupplierUser>());
        _dbContext.GetDbSet<VwSupplierUser>().Returns(dbSet);

        _httpClientService.PostAsync<UpsertUserCommand, int>(expectedEndpoint, command, Arg.Any<CancellationToken>())
            .Returns(0);

        // Act & Assert
        var exception = await Should.ThrowAsync<BadRequestException>(
            () => _service.CreateUser(command));

        exception.Message.ShouldBe("Failed to create user");
    }

    [Fact]
    public async Task CreateSupplierUserShouldRethrowExceptionWhenHttpClientThrows()
    {
        // Arrange
        var command = new UpsertUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            UserName = "johndoe",
            Password = "password123",
            LanguageId = 1,
            Status = true,
        };

        var expectedEndpoint = "/api/user/create-supplier-user";
        var expectedException = new HttpRequestException("Network error");

        // Mock DbSet to return no users (so username/email does not exist)
        var dbSet = CreateMockDbSet<VwSupplierUser>(new List<VwSupplierUser>());
        _dbContext.GetDbSet<VwSupplierUser>().Returns(dbSet);

        _httpClientService.PostAsync<UpsertUserCommand, int>(expectedEndpoint, command, Arg.Any<CancellationToken>())
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Should.ThrowAsync<HttpRequestException>(
            () => _service.CreateUser(command));

        exception.ShouldBe(expectedException);
    }

    [Fact]
    public async Task CreateSupplierUserShouldPassCancellationTokenWhenProvided()
    {
        // Arrange
        var command = new UpsertUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            UserName = "johndoe",
            Password = "password123",
            LanguageId = 1,
            Status = true,
        };

        var cancellationToken = new CancellationToken();
        var expectedEndpoint = "/api/user/create-supplier-user";

        // Mock DbSet to return no users (so username/email does not exist)
        var dbSet = CreateMockDbSet<VwSupplierUser>(new List<VwSupplierUser>());
        _dbContext.GetDbSet<VwSupplierUser>().Returns(dbSet);

        _httpClientService.PostAsync<UpsertUserCommand, int>(expectedEndpoint, command, cancellationToken)
            .Returns(123);

        // Act
        await _service.CreateUser(command, cancellationToken);

        // Assert
        await _httpClientService.Received(1).PostAsync<UpsertUserCommand, int>(expectedEndpoint, command, cancellationToken);
    }

    #endregion

    #region UpdateUser Tests

    [Fact]
    public async Task UpdateUserShouldReturnTrueWhenSuccessful()
    {
        // Arrange
        var userId = 123;
        var command = new UpsertUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            UserName = "johndoe",
            Password = "password123",
            LanguageId = 1,
            Status = true,
            Contacts = new ContactDetailCommand
            {
                Email = "<EMAIL>"
            }
        };

        var expectedEndpoint = "/api/user/123/update";

        // Mock DbSet to return no users (so username/email does not exist)
        var dbSet = CreateMockDbSet<VwSupplierUser>(new List<VwSupplierUser>());
        _dbContext.GetDbSet<VwSupplierUser>().Returns(dbSet);

        _httpClientService.PutAsync<UpsertUserCommand, object>(expectedEndpoint, command, Arg.Any<CancellationToken>())
            .Returns(Task.FromResult<object?>(null));

        // Act
        var result = await _service.UpdateUser(userId, command);

        // Assert
        result.ShouldBeTrue();

        // Verify the correct endpoint was called
        await _httpClientService.Received(1).PutAsync<UpsertUserCommand, object>(expectedEndpoint, command, Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task UpdateUserShouldPassCancellationTokenWhenProvided()
    {
        // Arrange
        var userId = 123;
        var command = new UpsertUserCommand
        {
            FirstName = "John",
            LastName = "Doe",
            UserName = "johndoe",
            Password = "password123",
            LanguageId = 1,
            Status = true,
        };

        var cancellationToken = new CancellationToken();
        var expectedEndpoint = "/api/user/123/update";

        // Mock DbSet to return no users (so username/email does not exist)
        var dbSet = CreateMockDbSet<VwSupplierUser>(new List<VwSupplierUser>());
        _dbContext.GetDbSet<VwSupplierUser>().Returns(dbSet);

        _httpClientService.PutAsync<UpsertUserCommand, object>(expectedEndpoint, command, cancellationToken)
            .Returns(Task.FromResult<object?>(null));

        // Act
        var result = await _service.UpdateUser(userId, command, cancellationToken);

        // Assert
        result.ShouldBeTrue();
        await _httpClientService.Received(1).PutAsync<UpsertUserCommand, object>(expectedEndpoint, command, cancellationToken);
    }

    #endregion

    #region UpdateUserStatusAsync Tests

    [Fact]
    public async Task UpdateUserStatusAsyncShouldCallCorrectEndpointWithCorrectParameters()
    {
        // Arrange
        var userId = 46;
        var isActive = true;
        var expectedEndpoint = $"/api/user/{userId}/update-supplier-user-status?isActive={isActive.ToString().ToUpper(System.Globalization.CultureInfo.InvariantCulture)}";

        _httpClientService
            .PutAsync<UpdateUserStatusCommand, bool>(
                expectedEndpoint,
                Arg.Is<UpdateUserStatusCommand>(c => c.IsActive == isActive),
                Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(true));

        // Act
        await _service.UpdateUserStatusAsync(userId, isActive);

        // Assert
        await _httpClientService.Received(1).PutAsync<UpdateUserStatusCommand, bool>(
            expectedEndpoint,
            Arg.Is<UpdateUserStatusCommand>(c => c.IsActive == isActive),
            Arg.Any<CancellationToken>());
    }

    #endregion

    #region Username exits and Email exits

    [Fact]
    public async Task EmailExistsReturnsTrueIfEmailExists()
    {
        // Arrange
        var email = "<EMAIL>";
        var expectedEndpoint = $"{_apiSettings.User.EmailExists}?email={Uri.EscapeDataString(email)}";
        _httpClientService.GetAsync<bool>(expectedEndpoint, Arg.Any<CancellationToken>()).Returns(true);

        // Act
        var result = await _service.EmailExists(email);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task EmailExistsReturnsFalseIfEmailDoesNotExist()
    {
        // Arrange
        var email = "<EMAIL>";
        var expectedEndpoint = $"{_apiSettings.User.EmailExists}?email={Uri.EscapeDataString(email)}";
        _httpClientService.GetAsync<bool>(expectedEndpoint, Arg.Any<CancellationToken>()).Returns(false);

        // Act
        var result = await _service.EmailExists(email);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task UsernameExistsReturnsTrueIfUsernameExists()
    {
        // Arrange
        var username = "TestUser";
        var expectedEndpoint = $"{_apiSettings.User.UsernameExists}?username={Uri.EscapeDataString(username)}";
        _httpClientService.GetAsync<bool>(expectedEndpoint, Arg.Any<CancellationToken>()).Returns(true);

        // Act
        var result = await _service.UsernameExists(username);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task UsernameExistsReturnsFalseIfUsernameDoesNotExist()
    {
        // Arrange
        var username = "NotFound";
        var expectedEndpoint = $"{_apiSettings.User.UsernameExists}?username={Uri.EscapeDataString(username)}";
        _httpClientService.GetAsync<bool>(expectedEndpoint, Arg.Any<CancellationToken>()).Returns(false);

        // Act
        var result = await _service.UsernameExists(username);

        // Assert
        result.ShouldBeFalse();
    }

    #endregion

    #region Helpers
    private static DbSet<T> CreateMockDbSet<T>(IEnumerable<T> data) where T : class
    {
        var queryable = data.AsQueryable();
        var asyncEnumerable = new TestAsyncEnumerable<T>(queryable);
        var dbSet = Substitute.For<DbSet<T>, IQueryable<T>, IAsyncEnumerable<T>>();
        ((IAsyncEnumerable<T>)dbSet).GetAsyncEnumerator(Arg.Any<CancellationToken>()).Returns(callInfo =>
            asyncEnumerable.GetAsyncEnumerator(callInfo.ArgAt<CancellationToken>(0)));
        ((IQueryable<T>)dbSet).Provider.Returns(new TestAsyncQueryProvider<T>(queryable.Provider));
        ((IQueryable<T>)dbSet).Expression.Returns(queryable.Expression);
        ((IQueryable<T>)dbSet).ElementType.Returns(queryable.ElementType);
        ((IQueryable<T>)dbSet).GetEnumerator().Returns(queryable.GetEnumerator());
        return dbSet;
    }

    #endregion

    [Fact]
    public async Task GetPasswordPolicyShouldReturnPolicyWhenSuccessful()
    {
        // Arrange
        var expectedPolicy = new PasswordPolicyDto
        {
            MinLength = 10,
            RequireUppercase = true,
            RequireLowercase = true,
            RequireDigit = true,
            RequireSpecialCharacter = true,
            AllowedSpecialCharacters = "!@#$%"
        };
        var expectedEndpoint = _apiSettings.User.GetPasswordPolicy;
        _httpClientService.GetAsync<PasswordPolicyDto>(expectedEndpoint, Arg.Any<CancellationToken>(), true)
            .Returns(expectedPolicy);

        // Act
        var result = await _service.GetPasswordPolicy();

        // Assert
        result.MinLength.ShouldBe(10);
    }
}